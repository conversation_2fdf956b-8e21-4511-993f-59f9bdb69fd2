import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/nickname.dart';
import '../services/database_service.dart';
import '../repositories/seller_repository.dart';
import '../repositories/event_repository.dart';
import '../models/seller.dart';
import '../utils/logger_utils.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import '../providers/seller_provider.dart';
import 'dart:io';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class NicknameNotifier extends StateNotifier<Nickname?> {
  final DatabaseService databaseService;
  final SellerRepository sellerRepository;
  
  // 프로필 이미지 동기화 캐시 (5분마다 체크)
  DateTime? _lastSyncCheck;
  static const Duration _syncCheckInterval = Duration(minutes: 5);
  
  // Firestore 실시간 리스너
  StreamSubscription<DocumentSnapshot>? _firestoreSubscription;
  
  NicknameNotifier(this.databaseService, this.sellerRepository) : super(null);

  @override
  void dispose() {
    _firestoreSubscription?.cancel();
    super.dispose();
  }

  /// 로그인/앱 시작 시 닉네임 자동 로드 및 실시간 동기화 설정
  Future<void> loadNickname() async {
    // 자동 로그아웃으로 인한 닉네임 초기화 플래그 확인
    await _checkAndHandleNicknameResetFlag();
    
    final result = await databaseService.safeTransaction((txn) async {
      return await txn.query('nicknames', limit: 1);
    }, taskName: 'loadNickname');
    
    String? dbProfileImageUrl;
    String? dbProfileImagePath;
    String? name;
    int? sellerId;
    if (result.isNotEmpty) {
      name = result.first['name'] as String?;
      sellerId = result.first['sellerId'] as int?;
      dbProfileImagePath = result.first['profileImagePath'] as String?;
      dbProfileImageUrl = result.first['profileImageUrl'] as String?;
      if (name != null && sellerId != null) {
        // 프로필 이미지 동기화 체크 제한 (5분마다만)
        final now = DateTime.now();
        final shouldCheckSync = _lastSyncCheck == null || 
            now.difference(_lastSyncCheck!) > _syncCheckInterval;
        
        if (!shouldCheckSync) {
          // 캐시된 상태 그대로 반환 (로그 없음)
          state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
          return;
        }
        
        _lastSyncCheck = now;
        
        // Firestore 실시간 리스너 설정 (한 번만)
        _setupRealtimeSync();
        
        // Firestore에서 최신 profileImageUrl 확인 (오류 시 무시)
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          try {
            final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
            final fsProfileImageUrl = doc.data()?['profileImageUrl'] as String?;

            // 로컬 파일 존재 여부 확인
            bool localFileExists = false;
            if (dbProfileImagePath != null && dbProfileImagePath.isNotEmpty) {
              localFileExists = await File(dbProfileImagePath).exists();
              if (!localFileExists) {
                LoggerUtils.logWarning('로컬 프로필 이미지 파일이 존재하지 않음: $dbProfileImagePath', tag: 'NicknameProvider');
                dbProfileImagePath = null; // 존재하지 않는 경로는 null로 처리
              }
            }

            // Firebase URL이 있고, (로컬 URL과 다르거나 로컬 파일이 없는 경우) 다운로드
            if (fsProfileImageUrl != null &&
                (fsProfileImageUrl != dbProfileImageUrl || !localFileExists)) {
              LoggerUtils.logInfo('프로필 이미지 다운로드 시작: $fsProfileImageUrl', tag: 'NicknameProvider');
              var url = fsProfileImageUrl + '?t=${DateTime.now().millisecondsSinceEpoch}';
              try {
                final dir = await getApplicationDocumentsDirectory();
                final timestamp = DateTime.now().millisecondsSinceEpoch;
                // 새로운 파일명 패턴: profile_{userId}_{timestamp}.jpg
                final file = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');
                final response = await http.get(Uri.parse(url));

                if (response.statusCode == 200) {
                  await file.writeAsBytes(response.bodyBytes);
                  dbProfileImagePath = file.path;

                  // 기존 프로필 이미지 파일들 정리 (최신 것만 유지)
                  final files = dir
                      .listSync()
                      .whereType<File>()
                      .where((f) => (f.path.contains('profile_image_${user.uid}_') || f.path.contains('profile_${user.uid}_')) && f.path.endsWith('.jpg'))
                      .toList();
                  files.sort((a, b) => b.path.compareTo(a.path));
                  for (int i = 1; i < files.length; i++) {
                    try { await files[i].delete(); } catch (_) {}
                  }

                  LoggerUtils.logInfo('프로필 이미지 다운로드 완료: ${file.path}', tag: 'NicknameProvider');
                } else {
                  LoggerUtils.logError('프로필 이미지 다운로드 실패: HTTP ${response.statusCode}', tag: 'NicknameProvider');
                }

                // DB에 최신 profileImageUrl, profileImagePath 저장 (실패해도 무시)
                try {
                  await databaseService.safeTransaction((txn) async {
                    await txn.execute('''
                      REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                      VALUES (?, ?, ?, ?)
                    ''', [name, sellerId, dbProfileImagePath, fsProfileImageUrl]);
                  });
                } catch (e) {
                  LoggerUtils.logWarning('닉네임 DB 업데이트 실패: $e', tag: 'NicknameProvider');
                }
                state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
                return;
              } catch (e) {
                LoggerUtils.logError('프로필 이미지 다운로드 실패: $e', tag: 'NicknameProvider');
                // 이미지 다운로드 실패해도 state는 유지
                state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
                return;
              }
            } else {
              // 동기화 불필요 (로그 완전 제거)
              state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
              return;
            }
          } catch (e) {
            // Firestore 접근 실패 시 로컬 데이터만 사용
            LoggerUtils.logWarning('Firestore 접근 실패, 로컬 데이터 사용: $e', tag: 'NicknameProvider');
            state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
            return;
          }
        }
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
        return;
      }
    }
    // Firestore에서 닉네임 동기화(최초 등록 등) - 오류 시 무시
    try {
      // Firebase 초기화 확인
      await Firebase.initializeApp();
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        try {
          final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
          if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
            name = doc.data()!['nickname'] as String;
            String? profileImagePath;
            String? profileImageUrl = doc.data()!['profileImageUrl'] as String?;
            if (profileImageUrl != null) {
              var url = profileImageUrl + '?t=${DateTime.now().millisecondsSinceEpoch}';
              try {
                final dir = await getApplicationDocumentsDirectory();
                final timestamp = DateTime.now().millisecondsSinceEpoch;
                // 새로운 파일명 패턴: profile_{userId}_{timestamp}.jpg
                final file = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');
                final response = await http.get(Uri.parse(url));
                await file.writeAsBytes(response.bodyBytes);
                profileImagePath = file.path;
                final files = dir
                    .listSync()
                    .whereType<File>()
                    .where((f) => (f.path.contains('profile_image_${user.uid}_') || f.path.contains('profile_${user.uid}_')) && f.path.endsWith('.jpg'))
                    .toList();
                files.sort((a, b) => b.path.compareTo(a.path));
                for (int i = 1; i < files.length; i++) {
                  try { await files[i].delete(); } catch (_) {}
                }
              } catch (_) {}
            }
            // sellers 테이블에 동일 이름의 판매자가 있는지 확인
            Seller? seller = await sellerRepository.getSellerByName(name);
            int sellerId;
            if (seller == null) {
              seller = Seller.create(name: name, isDefault: true);
              sellerId = await sellerRepository.insertSeller(seller);
            } else {
              sellerId = seller.id!;
            }
            // Firestore에서 닉네임을 불러온 경우 로컬 DB에도 저장 (실패해도 무시)
            try {
              await databaseService.safeTransaction((txn) async {
                await txn.execute('''
                  REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                  VALUES (?, ?, ?, ?)
                ''', [name, sellerId, profileImagePath, profileImageUrl]);
              });
            } catch (_) {}
            // ★ 파이어스토어에 닉네임이 있으면 무조건 state에 반영 (로컬DB 저장 실패해도)
            state = Nickname(name: name, sellerId: sellerId, profileImagePath: profileImagePath);
            return;
          }
        } catch (e) {
          // 네트워크 오류 등으로 파이어스토어 조회 실패 시에도 state를 null로 두지 않음 (로딩 상태는 별도 처리)
          LoggerUtils.logError(
            'Firestore 닉네임 조회 실패',
            error: e,
            tag: 'NicknameProvider',
          );
        }
      }
    } catch (e) {
      // Firebase 초기화 실패 시 로컬 DB만 사용
      LoggerUtils.logError(
        'Firebase 초기화 실패',
        error: e,
        tag: 'NicknameProvider',
      );
    }
    state = null;
  }

  /// 로그아웃 시 닉네임 상태 초기화
  void clearNickname() {
    LoggerUtils.logInfo('NicknameProvider 상태 초기화', tag: 'NicknameProvider');
    _firestoreSubscription?.cancel();
    _firestoreSubscription = null;
    _lastSyncCheck = null;
    state = null;
  }

  /// 강제로 닉네임 복구 시도 (디버깅/복구 목적)
  Future<bool> forceRecoverNickname() async {
    try {
      LoggerUtils.logInfo('강제 닉네임 복구 시도', tag: 'NicknameProvider');

      // Firestore에서 닉네임 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
        if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
          final name = doc.data()!['nickname'] as String;
          LoggerUtils.logInfo('Firestore에서 닉네임 발견: $name', tag: 'NicknameProvider');

          // sellers 테이블에서 판매자 확인/생성
          Seller? seller = await sellerRepository.getSellerByName(name);
          int sellerId;
          if (seller == null) {
            seller = Seller.create(name: name, isDefault: true);
            sellerId = await sellerRepository.insertSeller(seller);
          } else {
            sellerId = seller.id!;
          }

          // 로컬 DB에 저장
          await databaseService.safeTransaction((txn) async {
            await txn.execute('''
              REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
              VALUES (?, ?, ?, ?)
            ''', [name, sellerId, null, null]);
          });

          // 상태 복구
          state = Nickname(name: name, sellerId: sellerId, profileImagePath: null);
          LoggerUtils.logInfo('닉네임 강제 복구 성공: $name', tag: 'NicknameProvider');
          return true;
        }
      }

      LoggerUtils.logWarning('Firestore에 닉네임이 없어 복구 실패', tag: 'NicknameProvider');
      return false;
    } catch (e) {
      LoggerUtils.logError('강제 닉네임 복구 실패', tag: 'NicknameProvider', error: e);
      return false;
    }
  }

  Future<void> setNickname(String newName) async {
    final db = await databaseService.database;
    // 기존 닉네임 정보 로드
    final result = await db.query('nicknames', limit: 1);
    int? sellerId;
    String? profileImagePath;
    String? oldNickname;

    if (result.isNotEmpty) {
      sellerId = result.first['sellerId'] as int?;
      profileImagePath = result.first['profileImagePath'] as String?;
      oldNickname = result.first['name'] as String?;
    }

    if (sellerId == null) {
      // 최초 등록: 단순하게 기본 판매자 생성 (loadNickname과 동일한 방식)
      final seller = Seller.create(name: newName, isDefault: true);
      sellerId = await sellerRepository.insertSeller(seller);
    } else {
      // 기존 닉네임 변경: 모든 행사의 닉네임 기반 판매자 업데이트
      await _updateNicknameSellerForAllEvents(oldNickname, newName);
    }

    // 닉네임 테이블 갱신
    await databaseService.safeTransaction((txn) async {
      await txn.execute('''
        REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
        VALUES (?, ?, ?, ?)
      ''', [newName, sellerId, profileImagePath, null]);
    });
    state = Nickname(name: newName, sellerId: sellerId, profileImagePath: profileImagePath);

    // Firestore 동기화
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'nickname': newName,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    }
  }

  /// 프로필 이미지 업데이트 (업로드 후 즉시 상태 반영)
  Future<void> updateProfileImage(String localImagePath, String downloadUrl) async {
    try {
      final db = await databaseService.database;
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // 현재 닉네임 정보 가져오기
      final result = await db.query('nicknames', limit: 1);
      if (result.isEmpty) {
        // 닉네임이 없으면 loadNickname 호출
        await loadNickname();
        return;
      }

      final currentData = result.first;
      final name = currentData['name'] as String;
      final sellerId = currentData['sellerId'] as int;

      // REPLACE를 사용하여 UNIQUE 제약 문제 해결
      await databaseService.safeTransaction((txn) async {
        await txn.execute('''
          REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
          VALUES (?, ?, ?, ?)
        ''', [name, sellerId, localImagePath, downloadUrl]);
      });

      // 상태 즉시 업데이트
      state = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);

      LoggerUtils.logDebug(
        'NicknameProvider 프로필 이미지 상태 업데이트 완료: $localImagePath',
        tag: 'NicknameProvider',
      );
    } catch (e) {
      LoggerUtils.logError(
        'NicknameProvider 프로필 이미지 업데이트 실패',
        error: e,
        tag: 'NicknameProvider',
      );
      // 실패해도 loadNickname으로 복구 시도
      await loadNickname();
    }
  }

  /// 프로필 이미지 로컬 전용 업데이트 (Firebase 없이)
  Future<void> updateProfileImageLocal(String localImagePath) async {
    try {
      final db = await databaseService.database;
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // 현재 닉네임 정보 가져오기
      final result = await db.query('nicknames', limit: 1);
      if (result.isEmpty) {
        // 닉네임이 없으면 loadNickname 호출
        await loadNickname();
        return;
      }

      final currentData = result.first;
      final name = currentData['name'] as String;
      final sellerId = currentData['sellerId'] as int;

      // REPLACE를 사용하여 UNIQUE 제약 문제 해결 (URL은 null로 설정)
      await databaseService.safeTransaction((txn) async {
        await txn.execute('''
          REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
          VALUES (?, ?, ?, ?)
        ''', [name, sellerId, localImagePath, null]);
      });

      // 상태 즉시 업데이트
      state = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);

      LoggerUtils.logDebug(
        'NicknameProvider 프로필 이미지 로컬 상태 업데이트 완료: $localImagePath',
        tag: 'NicknameProvider',
      );
    } catch (e) {
      LoggerUtils.logError(
        'NicknameProvider 프로필 이미지 로컬 업데이트 실패',
        error: e,
        tag: 'NicknameProvider',
      );
      // 실패해도 loadNickname으로 복구 시도
      await loadNickname();
    }
  }

  /// 모든 행사의 닉네임 기반 판매자 업데이트
  Future<void> _updateNicknameSellerForAllEvents(String? oldNickname, String newNickname) async {
    try {
      if (oldNickname == null) return;
      
      // 모든 행사 목록 가져오기
      final eventRepository = EventRepository(databaseService);
      final events = await eventRepository.getAllEvents();
      
      for (final event in events) {
        // 각 행사에서 기존 닉네임 기반 판매자 찾기
        final existingSellers = await sellerRepository.getSellersByEventId(event.id!);
        final nicknameBasedSeller = existingSellers.where((s) => s.name == oldNickname && s.isDefault).firstOrNull;
        
        if (nicknameBasedSeller != null) {
          // 기존 판매자 이름 업데이트
          final updatedSeller = nicknameBasedSeller.copyWith(name: newNickname);
          await sellerRepository.updateSeller(updatedSeller);
        } else {
          // 기존 판매자가 없으면 새로 생성
          final newSeller = Seller.create(
            name: newNickname,
            isDefault: true,
            eventId: event.id!,
          );
          await sellerRepository.insertSeller(newSeller);
        }
      }
    } catch (e) {
      LoggerUtils.logError('모든 행사의 닉네임 기반 판매자 업데이트 실패', error: e);
    }
  }

  /// Firestore 실시간 동기화 설정 (프로필 이미지 포함)
  void _setupRealtimeSync() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null || _firestoreSubscription != null) return;

    try {
      LoggerUtils.logInfo('프로필 이미지 실시간 동기화 설정 시작', tag: 'NicknameProvider');
      
      _firestoreSubscription = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .snapshots()
          .listen(
        (snapshot) {
          _handleFirestoreProfileUpdate(snapshot);
        },
        onError: (error) {
          LoggerUtils.logError('프로필 이미지 실시간 동기화 에러', tag: 'NicknameProvider', error: error);
        },
      );
      
      LoggerUtils.logInfo('프로필 이미지 실시간 동기화 설정 완료', tag: 'NicknameProvider');
    } catch (e) {
      LoggerUtils.logError('프로필 이미지 실시간 동기화 설정 실패', tag: 'NicknameProvider', error: e);
    }
  }

  /// Firestore 프로필 변경사항 처리
  void _handleFirestoreProfileUpdate(DocumentSnapshot snapshot) async {
    try {
      if (!snapshot.exists) return;

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final data = snapshot.data() as Map<String, dynamic>?;
      final fsProfileImageUrl = data?['profileImageUrl'] as String?;
      
      if (fsProfileImageUrl == null) return;

      // 현재 로컬 상태 확인
      final currentNickname = state;
      if (currentNickname == null) return;

      // 로컬 DB에서 현재 URL 확인
      final db = await databaseService.database;
      final result = await db.query('nicknames', limit: 1);
      
      if (result.isEmpty) return;
      
      final dbProfileImageUrl = result.first['profileImageUrl'] as String?;
      
      // URL이 다르면 새 이미지 다운로드
      if (fsProfileImageUrl != dbProfileImageUrl) {
        LoggerUtils.logInfo('프로필 이미지 실시간 업데이트 감지: $fsProfileImageUrl', tag: 'NicknameProvider');
        
        try {
          final dir = await getApplicationDocumentsDirectory();
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final file = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');
          
          final response = await http.get(Uri.parse(fsProfileImageUrl));
          
          if (response.statusCode == 200) {
            await file.writeAsBytes(response.bodyBytes);
            
            // 기존 파일들 정리
            final files = dir
                .listSync()
                .whereType<File>()
                .where((f) => f.path.contains('profile_${user.uid}_') && f.path.endsWith('.jpg'))
                .toList();
            files.sort((a, b) => b.path.compareTo(a.path));
            for (int i = 1; i < files.length; i++) {
              try { await files[i].delete(); } catch (_) {}
            }
            
            // DB 업데이트
            await databaseService.safeTransaction((txn) async {
              await txn.execute('''
                REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                VALUES (?, ?, ?, ?)
              ''', [currentNickname.name, currentNickname.sellerId, file.path, fsProfileImageUrl]);
            });
            
            // 상태 업데이트
            state = currentNickname.copyWith(profileImagePath: file.path);
            
            LoggerUtils.logInfo('프로필 이미지 실시간 업데이트 완료', tag: 'NicknameProvider');
          }
        } catch (e) {
          LoggerUtils.logError('프로필 이미지 실시간 다운로드 실패', tag: 'NicknameProvider', error: e);
        }
      }
    } catch (e) {
      LoggerUtils.logError('프로필 변경사항 처리 실패', tag: 'NicknameProvider', error: e);
    }
  }

  /// 자동 로그아웃으로 인한 닉네임 초기화 플래그 확인 및 처리
  Future<void> _checkAndHandleNicknameResetFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final needsReset = prefs.getBool('nickname_reset_required') ?? false;

      if (needsReset) {
        LoggerUtils.logInfo('닉네임 초기화 플래그 감지 - Firestore에서 닉네임 복구 시도', tag: 'NicknameProvider');

        // Firestore에서 닉네임 복구 시도
        bool recovered = false;
        try {
          final user = FirebaseAuth.instance.currentUser;
          if (user != null) {
            final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
            if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
              final name = doc.data()!['nickname'] as String;
              LoggerUtils.logInfo('Firestore에서 닉네임 복구 성공: $name', tag: 'NicknameProvider');

              // sellers 테이블에서 판매자 확인/생성
              Seller? seller = await sellerRepository.getSellerByName(name);
              int sellerId;
              if (seller == null) {
                seller = Seller.create(name: name, isDefault: true);
                sellerId = await sellerRepository.insertSeller(seller);
              } else {
                sellerId = seller.id!;
              }

              // 로컬 DB에 저장
              await databaseService.safeTransaction((txn) async {
                await txn.execute('''
                  REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                  VALUES (?, ?, ?, ?)
                ''', [name, sellerId, null, null]);
              });

              // 상태 복구
              state = Nickname(name: name, sellerId: sellerId, profileImagePath: null);
              recovered = true;
            }
          }
        } catch (e) {
          LoggerUtils.logWarning('Firestore 닉네임 복구 실패: $e', tag: 'NicknameProvider');
        }

        if (!recovered) {
          // 복구 실패 시에만 상태 초기화
          LoggerUtils.logInfo('닉네임 복구 실패 - 상태 초기화', tag: 'NicknameProvider');
          state = null;
        }

        // 플래그 제거
        await prefs.remove('nickname_reset_required');

        LoggerUtils.logInfo('닉네임 초기화 플래그 처리 완료 (복구: $recovered)', tag: 'NicknameProvider');
      }
    } catch (e) {
      LoggerUtils.logError('닉네임 초기화 플래그 처리 실패', tag: 'NicknameProvider', error: e);
    }
  }
}

final nicknameProvider = StateNotifierProvider<NicknameNotifier, Nickname?>((ref) {
  final db = ref.watch(databaseServiceProvider);
  final sellerRepo = ref.watch(sellerRepositoryProvider);
  return NicknameNotifier(db, sellerRepo);
}); 