import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/event.dart';
import '../models/event_workspace.dart';
import '../repositories/event_repository.dart';
import '../services/user_settings_service.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 행사 워크스페이스 관리 서비스
///
/// 각 행사 워크스페이스는 완전히 독립적인 데이터 공간을 가집니다.
/// 사용자는 행사 워크스페이스 간을 자유롭게 전환할 수 있습니다.
/// 실제 데이터는 Event 테이블에 저장되지만, 행사 워크스페이스 개념으로 추상화됩니다.
class EventWorkspaceManager {
  static const String _tag = 'EventWorkspaceManager';
  static const String _currentWorkspaceIdKey = 'current_event_workspace_id';

  static EventWorkspaceManager? _instance;
  static EventWorkspaceManager get instance => _instance ??= EventWorkspaceManager._();

  EventWorkspaceManager._();

  EventRepository? _eventRepository;
  UserSettingsService? _userSettingsService;

  // 현재 활성 행사 워크스페이스
  EventWorkspace? _currentWorkspace;

  // 모든 행사 워크스페이스 목록
  List<EventWorkspace> _workspaces = [];

  // 상태 변경 리스너들
  final List<VoidCallback> _listeners = [];

  /// 현재 행사 워크스페이스 반환
  EventWorkspace? get currentWorkspace => _currentWorkspace;

  /// 모든 행사 워크스페이스 목록 반환
  List<EventWorkspace> get workspaces => List.unmodifiable(_workspaces);
  
  /// 워크스페이스 매니저 초기화
  Future<void> initialize() async {
    try {
      LoggerUtils.methodStart('initialize', tag: _tag);

      // EventRepository 초기화
      await _initializeRepository();

      // UserSettingsService 초기화
      _userSettingsService = UserSettingsService();

      // 저장된 워크스페이스 목록 로드
      await _loadWorkspaces();

      // 현재 워크스페이스 설정
      await _loadCurrentWorkspace();

      LoggerUtils.logInfo('행사 워크스페이스 매니저 초기화 완료 - 행사 워크스페이스 ${_workspaces.length}개', tag: _tag);
      LoggerUtils.methodEnd('initialize', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('행사 워크스페이스 매니저 초기화 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// EventRepository 초기화
  Future<void> _initializeRepository() async {
    if (_eventRepository == null) {
      final databaseService = DatabaseServiceImpl(); // factory constructor 사용
      _eventRepository = EventRepository(databaseService);
    }
  }
  
  /// 새 행사 워크스페이스 생성
  Future<EventWorkspace> createWorkspace({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    bool setAsCurrent = true,
    String? imagePath,
    String? description,
  }) async {
    try {
      LoggerUtils.methodStart('createWorkspace', tag: _tag, data: {'name': name});

      // Event 객체 생성
      final event = Event.create(
        name: name,
        startDate: startDate,
        endDate: endDate,
        isActive: true,
        imagePath: imagePath,
        description: description,
      );
      
      // 데이터베이스에 저장
      final savedEvent = await _eventRepository!.insertEvent(event);
      
      // 행사 워크스페이스 객체 생성
      final workspace = EventWorkspace.fromEvent(savedEvent);
      
      // 행사 워크스페이스 목록에 추가
      _workspaces.add(workspace);
      
      // 현재 행사 워크스페이스로 설정 (옵션)
      if (setAsCurrent) {
        await switchToWorkspace(workspace);
      }
      
      // 저장
      await _saveWorkspaces();
      
      // 리스너들에게 알림
      _notifyListeners();
      
      LoggerUtils.logInfo('행사 워크스페이스 생성 완료: ${workspace.name}', tag: _tag);
      LoggerUtils.methodEnd('createWorkspace', tag: _tag);

      return workspace;
    } catch (e, stackTrace) {
      LoggerUtils.logError('행사 워크스페이스 생성 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 행사 워크스페이스 전환
  Future<void> switchToWorkspace(EventWorkspace workspace, {bool fromFirebaseSync = false}) async {
    try {
      LoggerUtils.methodStart('switchToWorkspace', tag: _tag, data: {'workspaceId': workspace.id, 'name': workspace.name, 'fromFirebaseSync': fromFirebaseSync});

      if (_currentWorkspace?.id == workspace.id) {
        LoggerUtils.logInfo('이미 현재 행사 워크스페이스입니다: ${workspace.name}', tag: _tag);
        return;
      }

      final previousWorkspace = _currentWorkspace;
      _currentWorkspace = workspace;

      // SharedPreferences에 저장
      await _saveCurrentWorkspace();

      // 실시간 행사 전환 동기화 제거: Firebase에 저장하지 않음
      // 각 디바이스에서 독립적으로 행사 전환이 발생하며,
      // 앱 시작 시에만 마지막 선택된 행사로 진입합니다.
      LoggerUtils.logDebug('행사 전환 Firebase 저장 생략 (실시간 동기화 비활성화)', tag: _tag);

      LoggerUtils.logInfo('행사 워크스페이스 전환 완료: ${previousWorkspace?.name ?? 'null'} → ${workspace.name} (fromFirebaseSync: $fromFirebaseSync)', tag: _tag);

      // 리스너들에게 알림
      _notifyListeners();

      LoggerUtils.methodEnd('switchToWorkspace', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('행사 워크스페이스 전환 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 행사 워크스페이스 삭제
  Future<void> deleteWorkspace(EventWorkspace workspace) async {
    try {
      LoggerUtils.methodStart('deleteWorkspace', tag: _tag, data: {'workspaceId': workspace.id, 'name': workspace.name});
      
      // 데이터베이스에서 삭제
      await _eventRepository!.deleteEvent(workspace.id);
      
      // 목록에서 제거
      _workspaces.removeWhere((w) => w.id == workspace.id);
      
      // 현재 행사 워크스페이스였다면 다른 행사 워크스페이스로 전환
      if (_currentWorkspace?.id == workspace.id) {
        if (_workspaces.isNotEmpty) {
          await switchToWorkspace(_workspaces.first);
        } else {
          _currentWorkspace = null;
          await _saveCurrentWorkspace();
          await _saveCurrentWorkspaceToFirebase(); // Firebase에도 저장
        }
      }
      
      // 저장
      await _saveWorkspaces();
      
      // 리스너들에게 알림
      _notifyListeners();
      
      LoggerUtils.logInfo('행사 워크스페이스 삭제 완료: ${workspace.name}', tag: _tag);
      LoggerUtils.methodEnd('deleteWorkspace', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('행사 워크스페이스 삭제 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 상태 변경 리스너 추가
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }
  
  /// 상태 변경 리스너 제거
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }
  
  /// 리스너들에게 상태 변경 알림
  void _notifyListeners() {
    for (final listener in _listeners) {
      try {
        listener();
      } catch (e) {
        LoggerUtils.logError('리스너 실행 중 오류', tag: _tag, error: e);
      }
    }
  }
  
  /// 행사 워크스페이스 목록 로드
  Future<void> _loadWorkspaces() async {
    try {
      // 데이터베이스에서 모든 이벤트 로드
      final events = await _eventRepository!.getAllEvents();
      _workspaces = events.map((event) => EventWorkspace.fromEvent(event)).toList();
      
      LoggerUtils.logInfo('행사 워크스페이스 목록 로드 완료: ${_workspaces.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 워크스페이스 목록 로드 실패', tag: _tag, error: e);
      _workspaces = [];
    }
  }
  
  /// 현재 행사 워크스페이스 로드
  Future<void> _loadCurrentWorkspace() async {
    try {
      // 1. Firebase에서 먼저 로드 시도 (실시간 동기화)
      await _loadCurrentWorkspaceFromFirebase();

      // 2. Firebase에서 로드되지 않았으면 SharedPreferences에서 로드
      if (_currentWorkspace == null) {
        final prefs = await SharedPreferences.getInstance();
        final currentWorkspaceId = prefs.getInt(_currentWorkspaceIdKey);

        if (currentWorkspaceId != null) {
          _currentWorkspace = _workspaces.where((w) => w.id == currentWorkspaceId).firstOrNull;
        }
      }

      // 3. 현재 행사 워크스페이스가 없고 행사 워크스페이스가 존재하면 첫 번째를 현재로 설정
      if (_currentWorkspace == null && _workspaces.isNotEmpty) {
        _currentWorkspace = _workspaces.first;
        await _saveCurrentWorkspace();
        // Firebase 저장 제거: 실시간 동기화 비활성화
      }

      LoggerUtils.logInfo('현재 행사 워크스페이스 로드: ${_currentWorkspace?.name ?? 'null'}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('현재 행사 워크스페이스 로드 실패', tag: _tag, error: e);
    }
  }
  
  /// 행사 워크스페이스 목록 저장
  Future<void> _saveWorkspaces() async {
    try {
      // 실제로는 데이터베이스에 이미 저장되어 있으므로 별도 저장 불필요
      LoggerUtils.logDebug('행사 워크스페이스 목록 저장 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 워크스페이스 목록 저장 실패', tag: _tag, error: e);
    }
  }
  
  /// 현재 행사 워크스페이스 저장
  Future<void> _saveCurrentWorkspace() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentWorkspace != null) {
        await prefs.setInt(_currentWorkspaceIdKey, _currentWorkspace!.id);
      } else {
        await prefs.remove(_currentWorkspaceIdKey);
      }
      LoggerUtils.logDebug('현재 행사 워크스페이스 저장 완료: ${_currentWorkspace?.name ?? 'null'}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('현재 행사 워크스페이스 저장 실패', tag: _tag, error: e);
    }
  }

  /// 현재 워크스페이스를 Firebase에 저장 (실시간 동기화용)
  Future<void> _saveCurrentWorkspaceToFirebase() async {
    try {
      if (_userSettingsService == null) {
        LoggerUtils.logWarning('UserSettingsService가 초기화되지 않았습니다', tag: _tag);
        return;
      }

      await _userSettingsService!.updateLastWorkspaceId(
        _currentWorkspace?.id,
        deviceId: 'device_${DateTime.now().millisecondsSinceEpoch}',
      );

      LoggerUtils.logDebug('현재 워크스페이스 Firebase 저장 완료: ${_currentWorkspace?.name ?? 'null'}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('현재 워크스페이스 Firebase 저장 실패', tag: _tag, error: e);
      // Firebase 저장 실패해도 로컬 동작에는 영향을 주지 않음
    }
  }

  /// Firebase에서 마지막 워크스페이스 정보 로드 및 적용
  Future<void> _loadCurrentWorkspaceFromFirebase() async {
    try {
      if (_userSettingsService == null) {
        LoggerUtils.logWarning('UserSettingsService가 초기화되지 않았습니다', tag: _tag);
        return;
      }

      final userSettings = await _userSettingsService!.downloadUserSettings();
      if (userSettings?.lastWorkspaceId != null) {
        final workspaceId = userSettings!.lastWorkspaceId!;
        final workspace = _workspaces.where((w) => w.id == workspaceId).firstOrNull;

        if (workspace != null) {
          LoggerUtils.logInfo('Firebase에서 마지막 워크스페이스 복원: ${workspace.name}', tag: _tag);
          _currentWorkspace = workspace;

          // SharedPreferences에도 동기화
          await _saveCurrentWorkspace();
        } else {
          LoggerUtils.logWarning('Firebase의 마지막 워크스페이스 ID($workspaceId)에 해당하는 워크스페이스를 찾을 수 없습니다', tag: _tag);
        }
      }
    } catch (e) {
      LoggerUtils.logError('Firebase에서 마지막 워크스페이스 로드 실패', tag: _tag, error: e);
      // Firebase 로드 실패해도 로컬 동작에는 영향을 주지 않음
    }
  }
}
