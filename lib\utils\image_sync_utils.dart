import 'dart:async';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/database_service.dart';
import 'logger_utils.dart';

/// 이미지 동기화 공통 유틸리티
///
/// Firebase Storage와의 이미지 업로드/다운로드를 통합 관리하며,
/// 일관된 파일명 규칙과 에러 처리를 제공합니다.
class ImageSyncUtils {
  static const String _tag = 'ImageSyncUtils';

  // Firebase 인스턴스
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final Connectivity _connectivity = Connectivity();



  /// 상품 이미지를 Firebase Storage에 업로드 (새로운 방식)
  ///
  /// [eventId]: 행사 ID
  /// [categoryName]: 카테고리명
  /// [productName]: 상품명
  /// [localImagePath]: 로컬 이미지 경로
  ///
  /// Returns: 업로드된 이미지의 다운로드 URL 또는 null
  static Future<String?> uploadProductImageWithName(
    int eventId,
    String categoryName,
    String productName,
    String localImagePath
  ) async {
    final sanitizedFileName = _sanitizeFileName('${categoryName}_${productName}.jpg');
    return _uploadImage(
      localImagePath: localImagePath,
      storagePath: 'users/${_getCurrentUserId()}/events/$eventId/products/$sanitizedFileName',
      logContext: '상품 이미지 ($categoryName - $productName)',
    );
  }

  /// 상품 객체에서 카테고리 ID를 조회하여 이미지 업로드 (편의 함수)
  ///
  /// [eventId]: 행사 ID
  /// [categoryId]: 카테고리 ID
  /// [productName]: 상품명
  /// [localImagePath]: 로컬 이미지 경로
  ///
  /// Returns: 업로드된 이미지의 다운로드 URL 또는 null
  static Future<String?> uploadProductImageWithCategoryId(
    int eventId,
    int categoryId,
    String productName,
    String localImagePath
  ) async {
    LoggerUtils.logInfo('상품 이미지 업로드 시작: $productName (카테고리ID: $categoryId)', tag: _tag);

    final categoryName = await _getCategoryNameById(categoryId);
    LoggerUtils.logDebug('조회된 카테고리명: $categoryName', tag: _tag);

    return uploadProductImageWithName(eventId, categoryName, productName, localImagePath);
  }

  /// 행사 이미지를 Firebase Storage에 업로드
  ///
  /// [eventId]: 행사 ID
  /// [localImagePath]: 로컬 이미지 경로
  ///
  /// Returns: 업로드된 이미지의 다운로드 URL 또는 null
  static Future<String?> uploadEventImage(
    int eventId,
    String localImagePath
  ) async {
    return _uploadImage(
      localImagePath: localImagePath,
      storagePath: 'users/${_getCurrentUserId()}/events/$eventId/event_image.jpg',
      logContext: '행사 이미지 (행사ID: $eventId)',
    );
  }

  /// 상품 이름 변경 시 Firebase Storage 파일 이름도 변경 (복사 방식으로 비용 절약)
  ///
  /// [eventId]: 행사 ID
  /// [oldCategoryName]: 기존 카테고리명
  /// [oldProductName]: 기존 상품명
  /// [newCategoryName]: 새 카테고리명
  /// [newProductName]: 새 상품명
  /// [currentImageUrl]: 현재 이미지 URL (Firebase Storage URL)
  ///
  /// Returns: 새로운 이미지 URL 또는 기존 URL
  static Future<String?> renameProductImageInStorage(
    int eventId,
    String oldCategoryName,
    String oldProductName,
    String newCategoryName,
    String newProductName,
    String? currentImageUrl
  ) async {
    try {
      LoggerUtils.logInfo('상품 이미지 파일명 변경 시작: $oldCategoryName/$oldProductName → $newCategoryName/$newProductName', tag: _tag);

      // Firebase URL이 아니면 처리하지 않음
      if (currentImageUrl == null || !currentImageUrl.startsWith('http')) {
        LoggerUtils.logInfo('로컬 이미지이므로 파일명 변경 불필요', tag: _tag);
        return currentImageUrl;
      }

      // 기존 파일 경로와 새 파일 경로 생성
      final oldFileName = _sanitizeFileName('${oldCategoryName}_${oldProductName}.jpg');
      final newFileName = _sanitizeFileName('${newCategoryName}_${newProductName}.jpg');

      // 파일명이 같으면 변경 불필요
      if (oldFileName == newFileName) {
        LoggerUtils.logInfo('파일명이 동일하므로 변경 불필요: $oldFileName', tag: _tag);
        return currentImageUrl;
      }

      final userId = _getCurrentUserId();
      final oldStoragePath = 'users/$userId/events/$eventId/products/$oldFileName';
      final newStoragePath = 'users/$userId/events/$eventId/products/$newFileName';

      // Firebase Storage에서 파일 복사 (재업로드 없이 이름만 변경)
      try {
        final oldRef = _storage.ref().child(oldStoragePath);
        final newRef = _storage.ref().child(newStoragePath);

        // 기존 파일 존재 확인
        try {
          await oldRef.getMetadata();
        } catch (e) {
          LoggerUtils.logWarning('기존 파일이 존재하지 않음: $oldStoragePath', tag: _tag);
          return currentImageUrl; // 기존 URL 그대로 반환
        }

        // 파일 데이터 다운로드
        final data = await oldRef.getData();
        if (data == null) {
          LoggerUtils.logWarning('파일 데이터를 가져올 수 없음: $oldStoragePath', tag: _tag);
          return currentImageUrl;
        }

        // 새 위치에 업로드 (메타데이터 유지)
        final metadata = await oldRef.getMetadata();
        final uploadTask = newRef.putData(
          data,
          SettableMetadata(
            contentType: metadata.contentType,
            customMetadata: metadata.customMetadata,
          ),
        );

        await uploadTask;
        final newDownloadUrl = await newRef.getDownloadURL();

        // 기존 파일 삭제
        await oldRef.delete();

        LoggerUtils.logInfo('이미지 파일명 변경 완료: $oldFileName → $newFileName', tag: _tag);
        return newDownloadUrl;

      } catch (e) {
        LoggerUtils.logError('Firebase Storage 파일 복사 실패', tag: _tag, error: e);
        return currentImageUrl; // 실패 시 기존 URL 반환
      }

    } catch (e) {
      LoggerUtils.logError('상품 이미지 파일명 변경 실패', tag: _tag, error: e);
      return currentImageUrl;
    }
  }

  /// 카테고리 이름 변경 시 해당 카테고리의 모든 상품 이미지 파일명 변경 (복사 방식)
  ///
  /// [eventId]: 행사 ID
  /// [oldCategoryName]: 기존 카테고리명
  /// [newCategoryName]: 새 카테고리명
  /// [products]: 해당 카테고리의 상품 리스트 (name, currentImageUrl)
  ///
  /// Returns: 업데이트된 상품별 이미지 URL 맵
  static Future<Map<String, String?>> renameCategoryProductImagesInStorage(
    int eventId,
    String oldCategoryName,
    String newCategoryName,
    List<MapEntry<String, String?>> products
  ) async {
    try {
      LoggerUtils.logInfo('카테고리 이미지 파일명 일괄 변경 시작: $oldCategoryName → $newCategoryName (${products.length}개 상품)', tag: _tag);

      final results = <String, String?>{};

      // 병렬 처리로 성능 향상 (5개씩 배치)
      const batchSize = 5;
      for (int i = 0; i < products.length; i += batchSize) {
        final batch = products.skip(i).take(batchSize).toList();

        final batchResults = await Future.wait(
          batch.map((product) async {
            final productName = product.key;
            final currentImageUrl = product.value;

            try {
              final newImageUrl = await renameProductImageInStorage(
                eventId,
                oldCategoryName,
                productName,
                newCategoryName,
                productName,
                currentImageUrl,
              );
              return MapEntry(productName, newImageUrl);
            } catch (e) {
              LoggerUtils.logWarning('상품 $productName 이미지 파일명 변경 실패', tag: _tag, error: e);
              return MapEntry(productName, currentImageUrl); // 실패 시 기존 URL 유지
            }
          }),
        );

        for (final result in batchResults) {
          results[result.key] = result.value;
        }
      }

      LoggerUtils.logInfo('카테고리 이미지 파일명 일괄 변경 완료: ${results.length}개 처리', tag: _tag);
      return results;
    } catch (e) {
      LoggerUtils.logError('카테고리 이미지 파일명 일괄 변경 실패', tag: _tag, error: e);
      return {};
    }
  }

  /// 상품 이미지를 Firebase Storage에서 다운로드 (재시도 로직 포함)
  ///
  /// [eventId]: 행사 ID
  /// [productId]: 상품 ID (기존 호환성을 위해 유지)
  /// [downloadUrl]: Firebase Storage 다운로드 URL
  ///
  /// Returns: 로컬에 저장된 이미지 경로 또는 null
  static Future<String?> downloadProductImage(
    int eventId,
    int productId,
    String downloadUrl
  ) async {
    return _downloadImageWithRetry(
      downloadUrl: downloadUrl,
      localDir: 'product_images/$eventId',
      fileName: '${productId}_image.jpg',
      logContext: '상품 이미지 (상품ID: $productId)',
    );
  }

  /// 대량 상품 이미지 업로드 (새로운 방식 - 카테고리명_상품명.jpg)
  ///
  /// [eventId]: 행사 ID
  /// [imageDataList]: 업로드할 이미지 데이터 리스트 (categoryName, productName, localImagePath)
  /// [onProgress]: 진행률 콜백
  ///
  /// Returns: 업로드된 이미지 URL 맵 (productName -> downloadUrl)
  static Future<Map<String, String?>> bulkUploadProductImagesWithName(
    int eventId,
    List<MapEntry<String, MapEntry<String, String>>> imageDataList, {
    Function(int current, int total)? onProgress,
  }) async {
    const String logContext = '대량 상품 이미지 업로드 (새로운 방식)';
    LoggerUtils.logInfo('$logContext 시작: ${imageDataList.length}개', tag: _tag);

    final results = <String, String?>{};

    try {
      // 20개씩 배치로 나누어 처리 (Firebase 제한 고려)
      const batchSize = 20;
      for (int i = 0; i < imageDataList.length; i += batchSize) {
        final batch = imageDataList.skip(i).take(batchSize).toList();

        // 배치 내에서 병렬 업로드
        final batchTasks = batch.map((entry) => () async {
          final productName = entry.key;
          final categoryName = entry.value.key;
          final imagePath = entry.value.value;

          try {
            final uploadedUrl = await uploadProductImageWithName(eventId, categoryName, productName, imagePath);
            return MapEntry(productName, uploadedUrl);
          } catch (e) {
            LoggerUtils.logWarning('상품 $productName 이미지 업로드 실패', tag: _tag, error: e);
            return MapEntry(productName, null);
          }
        }).toList();

        // 배치 실행 (15개 동시 업로드)
        final batchResults = await Future.wait(batchTasks.map((task) => task()));

        // 결과 수집
        for (final result in batchResults) {
          results[result.key] = result.value;
        }

        // 진행률 업데이트
        final currentProgress = i + batch.length;
        onProgress?.call(currentProgress, imageDataList.length);

        // 배치 간 잠시 대기 (Firebase 부하 분산)
        if (i + batchSize < imageDataList.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      final successCount = results.values.where((url) => url != null).length;
      LoggerUtils.logInfo('$logContext 완료: $successCount/${imageDataList.length}개 성공', tag: _tag);
      return results;

    } catch (e) {
      LoggerUtils.logError('$logContext 실패', tag: _tag, error: e);
      return {};
    }
  }

  /// 행사 이미지를 Firebase Storage에서 다운로드 (재시도 로직 포함)
  ///
  /// [eventId]: 행사 ID
  /// [downloadUrl]: Firebase Storage 다운로드 URL
  ///
  /// Returns: 로컬에 저장된 이미지 경로 또는 null
  static Future<String?> downloadEventImage(
    int eventId,
    String downloadUrl
  ) async {
    return _downloadImageWithRetry(
      downloadUrl: downloadUrl,
      localDir: 'event_images',
      fileName: '${eventId}_image.jpg',
      logContext: '행사 이미지 (행사ID: $eventId)',
    );
  }

  /// 공통 이미지 업로드 로직
  static Future<String?> _uploadImage({
    required String localImagePath,
    required String storagePath,
    required String logContext,
  }) async {
    try {
      // 1. 기본 검증
      if (localImagePath.isEmpty) {
        LoggerUtils.logWarning('이미지 경로가 비어있음: $logContext', tag: _tag);
        return null;
      }

      // 2. 이미 Firebase URL인 경우 그대로 반환
      if (localImagePath.startsWith('http')) {
        LoggerUtils.logDebug('이미 업로드된 이미지: $logContext', tag: _tag);
        return localImagePath;
      }

      // 3. 파일 존재 확인
      final file = File(localImagePath);
      if (!await file.exists()) {
        LoggerUtils.logWarning('이미지 파일이 존재하지 않음: $localImagePath ($logContext)', tag: _tag);
        return null; // 파일이 없으면 null 반환하여 서버에 잘못된 경로가 저장되지 않도록 함
      }

      // 4. 사용자 로그인 확인
      final user = _auth.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않음: $logContext', tag: _tag);
        return null; // 로그인되지 않은 경우 null 반환
      }

      // 5. 네트워크 상태 확인
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        LoggerUtils.logInfo('네트워크 연결 없음, 업로드 불가: $logContext', tag: _tag);
        return null; // 로컬 경로 대신 null 반환하여 서버에 로컬 경로가 저장되지 않도록 함
      }

      // 6. Firebase Storage 업로드
      final storageRef = _storage.ref().child(storagePath);

      final uploadTask = storageRef.putFile(file);
      final snapshot = await uploadTask.timeout(
        const Duration(seconds: 15), // 판매 현장 최적화: 빠른 타임아웃
        onTimeout: () {
          LoggerUtils.logWarning('이미지 업로드 타임아웃: $logContext', tag: _tag);
          throw TimeoutException('업로드 타임아웃', const Duration(seconds: 15));
        },
      );

      final downloadUrl = await snapshot.ref.getDownloadURL();
      LoggerUtils.logInfo('이미지 업로드 성공: $logContext → $downloadUrl', tag: _tag);
      return downloadUrl;

    } catch (e) {
      LoggerUtils.logError('이미지 업로드 실패: $logContext', tag: _tag, error: e);
      return null; // 실패 시 null 반환하여 서버에 로컬 경로가 저장되지 않도록 함
    }
  }

  /// 재시도 로직이 포함된 이미지 다운로드
  static Future<String?> _downloadImageWithRetry({
    required String downloadUrl,
    required String localDir,
    required String fileName,
    required String logContext,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final result = await _downloadImage(
          downloadUrl: downloadUrl,
          localDir: localDir,
          fileName: fileName,
          logContext: logContext,
        );

        if (result != null) {
          if (attempt > 1) {
            LoggerUtils.logInfo('이미지 다운로드 재시도 성공 ($attempt/$maxRetries): $logContext', tag: _tag);
          }
          return result;
        }

        // null 반환은 재시도하지 않음 (URL 검증 실패 등)
        if (attempt == 1) {
          LoggerUtils.logWarning('이미지 다운로드 실패 (재시도 안함): $logContext', tag: _tag);
        }
        return null;

      } catch (e) {
        if (attempt == maxRetries) {
          LoggerUtils.logError('이미지 다운로드 최종 실패 ($attempt/$maxRetries): $logContext', tag: _tag, error: e);
          return null;
        }

        final delay = Duration(seconds: initialDelay.inSeconds * attempt);
        LoggerUtils.logWarning('이미지 다운로드 재시도 예정 ($attempt/$maxRetries): $logContext, ${delay.inSeconds}초 후 재시도', tag: _tag, error: e);
        await Future.delayed(delay);
      }
    }
    return null;
  }

  /// 공통 이미지 다운로드 로직 (개선된 버전)
  static Future<String?> _downloadImage({
    required String downloadUrl,
    required String localDir,
    required String fileName,
    required String logContext,
  }) async {
    try {
      // 1. URL 검증
      if (downloadUrl.isEmpty || !downloadUrl.startsWith('http')) {
        LoggerUtils.logWarning('유효하지 않은 다운로드 URL: $downloadUrl ($logContext)', tag: _tag);
        return null;
      }

      // 2. 로컬 저장 경로 생성
      final appDir = await _getApplicationDocumentsDirectory();
      final localDirPath = Directory('${appDir.path}/$localDir');
      if (!await localDirPath.exists()) {
        await localDirPath.create(recursive: true);
      }

      final localFile = File('${localDirPath.path}/$fileName');

      // 3. 이미 파일이 존재하면 기존 파일 사용
      if (await localFile.exists()) {
        LoggerUtils.logDebug('이미지 이미 존재: $logContext → ${localFile.path}', tag: _tag);
        return localFile.path;
      }

      // 4. 네트워크 상태 확인
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        LoggerUtils.logWarning('네트워크 연결 없음, 다운로드 불가: $logContext', tag: _tag);
        return null;
      }

      // 5. Firebase Storage에서 다운로드 (타임아웃 90초로 증가)
      final storageRef = _storage.refFromURL(downloadUrl);
      await storageRef.writeToFile(localFile).timeout(
        const Duration(seconds: 90),
        onTimeout: () {
          LoggerUtils.logWarning('이미지 다운로드 타임아웃 (90초): $logContext', tag: _tag);
          throw TimeoutException('다운로드 타임아웃', const Duration(seconds: 90));
        },
      );

      LoggerUtils.logInfo('이미지 다운로드 성공: $logContext → ${localFile.path}', tag: _tag);
      return localFile.path;

    } catch (e) {
      LoggerUtils.logError('이미지 다운로드 실패: $logContext', tag: _tag, error: e);
      return null;
    }
  }

  /// 현재 사용자 ID 가져오기
  static String? _getCurrentUserId() {
    return _auth.currentUser?.uid;
  }

  /// 파일명을 안전한 형태로 정리
  /// 특수문자, 공백 등을 제거하고 파일 시스템에 안전한 이름으로 변환
  static String _sanitizeFileName(String fileName) {
    LoggerUtils.logDebug('파일명 정리 시작: $fileName', tag: _tag);

    // 1. 기본 정리: 위험한 특수문자만 언더스코어로 변경 (한글 보존)
    String sanitized = fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')  // Windows/Unix 금지 문자
        .replaceAll(RegExp(r'\s+'), '_')           // 공백을 언더스코어로
        .replaceAll(RegExp(r'[^\w가-힣ㄱ-ㅎㅏ-ㅣ\-_.]', unicode: true), '_')  // 한글, 영문, 숫자, 하이픈, 언더스코어, 점만 허용
        .replaceAll(RegExp(r'_+'), '_')            // 연속된 언더스코어를 하나로
        .replaceAll(RegExp(r'^_+|_+$'), '');       // 시작/끝 언더스코어 제거

    LoggerUtils.logDebug('1단계 정리 후: $sanitized', tag: _tag);

    // 2. 길이 제한 (확장자 포함 100자)
    if (sanitized.length > 100) {
      final dotIndex = sanitized.lastIndexOf('.');
      if (dotIndex > 0) {
        final extension = sanitized.substring(dotIndex);
        final nameWithoutExt = sanitized.substring(0, dotIndex);
        sanitized = '${nameWithoutExt.substring(0, 100 - extension.length)}$extension';
      } else {
        sanitized = sanitized.substring(0, 100);
      }
      LoggerUtils.logDebug('길이 제한 후: $sanitized', tag: _tag);
    }

    // 3. 빈 파일명 방지
    if (sanitized.isEmpty || sanitized == '.jpg') {
      sanitized = 'product_${DateTime.now().millisecondsSinceEpoch}.jpg';
      LoggerUtils.logWarning('빈 파일명으로 fallback 사용: $sanitized', tag: _tag);
    }

    LoggerUtils.logDebug('최종 파일명: $sanitized', tag: _tag);
    return sanitized;
  }

  /// 애플리케이션 문서 디렉토리 가져오기
  static Future<Directory> _getApplicationDocumentsDirectory() async {
    try {
      return await getApplicationDocumentsDirectory();
    } catch (e) {
      LoggerUtils.logWarning('getApplicationDocumentsDirectory 실패, 폴백 사용', tag: _tag, error: e);
      return Directory.systemTemp;
    }
  }

  /// 이미지 경로가 로컬 파일인지 확인
  static bool isLocalImagePath(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return false;
    return !imagePath.startsWith('http');
  }

  /// 이미지 경로가 네트워크 URL인지 확인
  static bool isNetworkImagePath(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return false;
    return imagePath.startsWith('http');
  }

  /// 로컬 이미지 파일이 존재하는지 확인
  static Future<bool> localImageExists(String imagePath) async {
    if (!isLocalImagePath(imagePath)) return false;
    return await File(imagePath).exists();
  }

  /// 이미지 캐시 정리 (특정 행사)
  static Future<void> clearEventImageCache(int eventId) async {
    try {
      final appDir = await _getApplicationDocumentsDirectory();

      // 상품 이미지 캐시 정리
      final productImagesDir = Directory('${appDir.path}/product_images/$eventId');
      if (await productImagesDir.exists()) {
        await productImagesDir.delete(recursive: true);
        LoggerUtils.logInfo('행사 $eventId 상품 이미지 캐시 정리 완료', tag: _tag);
      }

      // 행사 이미지 캐시 정리
      final eventImageFile = File('${appDir.path}/event_images/${eventId}_image.jpg');
      if (await eventImageFile.exists()) {
        await eventImageFile.delete();
        LoggerUtils.logInfo('행사 $eventId 이미지 캐시 정리 완료', tag: _tag);
      }

    } catch (e) {
      LoggerUtils.logError('행사 $eventId 이미지 캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// 전체 이미지 캐시 정리
  static Future<void> clearAllImageCache() async {
    try {
      final appDir = await _getApplicationDocumentsDirectory();

      // 상품 이미지 캐시 정리
      final productImagesDir = Directory('${appDir.path}/product_images');
      if (await productImagesDir.exists()) {
        await productImagesDir.delete(recursive: true);
      }

      // 행사 이미지 캐시 정리
      final eventImagesDir = Directory('${appDir.path}/event_images');
      if (await eventImagesDir.exists()) {
        await eventImagesDir.delete(recursive: true);
      }

      LoggerUtils.logInfo('전체 이미지 캐시 정리 완료', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('전체 이미지 캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// 카테고리 ID로 카테고리명 조회
  ///
  /// [categoryId]: 카테고리 ID
  ///
  /// Returns: 카테고리명 또는 '기본 카테고리' (조회 실패시)
  static Future<String> _getCategoryNameById(int categoryId) async {
    try {
      LoggerUtils.logDebug('카테고리 조회 시작: categoryId=$categoryId', tag: _tag);

      // DatabaseService 인스턴스 생성 (직접 접근)
      final databaseService = DatabaseServiceImpl();
      final db = await databaseService.database;

      final result = await db.query(
        DatabaseServiceImpl.categoriesTable,
        columns: ['name'],
        where: 'id = ?',
        whereArgs: [categoryId],
        limit: 1,
      );

      if (result.isNotEmpty) {
        final categoryName = result.first['name'] as String;
        LoggerUtils.logDebug('카테고리 조회 성공: $categoryName', tag: _tag);
        return categoryName;
      }

      LoggerUtils.logWarning('카테고리 ID $categoryId를 찾을 수 없음', tag: _tag);
      return '기본카테고리';

    } catch (e) {
      LoggerUtils.logError('카테고리 조회 실패: categoryId=$categoryId', tag: _tag, error: e);
      return '기본카테고리';
    }
  }
}
