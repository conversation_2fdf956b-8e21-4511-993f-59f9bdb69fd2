# Blue Booth Manager - API 문서

## 📋 개요

이 문서는 Blue Booth Manager의 주요 클래스들의 API를 설명합니다. Provider, Repository, Service 계층의 모든 공개 메서드와 사용법을 포함합니다.

## 🏗️ API 구조

### 계층별 API 분류
- **Provider API**: 상태 관리 및 UI 연동
- **Repository API**: 데이터 접근 및 CRUD
- **Service API**: 인프라 서비스 및 유틸리티
- **Business Logic API**: 비즈니스 로직 처리

---

## 🔄 Provider API

### ProductProvider

상품 관련 상태를 관리하는 Provider입니다.

#### 클래스 정보
```dart
class ProductProvider extends OptimizedProvider<ProductState>
```

#### 주요 메서드

##### `loadProducts()`
상품 목록을 로드합니다.

```dart
Future<void> loadProducts()
```

**반환값**: `Future<void>`

**예외**:
- `ProviderException`: 데이터베이스 연결 실패
- `NetworkException`: 네트워크 오류

**사용 예시**:
```dart
final productProvider = ref.read(productProviderProvider.notifier);
await productProvider.loadProducts();
```

##### `addProduct(Product product)`
새로운 상품을 추가합니다.

```dart
Future<void> addProduct(Product product)
```

**파라미터**:
- `product` (Product): 추가할 상품 객체

**반환값**: `Future<void>`

**예외**:
- `ValidationException`: 상품 데이터 유효성 검증 실패
- `ProviderException`: 데이터베이스 저장 실패

**사용 예시**:
```dart
final product = Product(
  name: '새 상품',
  price: 10000,
  quantity: 50,
  sellerName: '판매자',
);
await productProvider.addProduct(product);
```

##### `updateProduct(Product product)`
기존 상품을 수정합니다.

```dart
Future<void> updateProduct(Product product)
```

**파라미터**:
- `product` (Product): 수정할 상품 객체 (ID 필수)

**반환값**: `Future<void>`

**예외**:
- `ValidationException`: 상품 데이터 유효성 검증 실패
- `NotFoundException`: 상품을 찾을 수 없음
- `ProviderException`: 데이터베이스 수정 실패

**사용 예시**:
```dart
final updatedProduct = product.copyWith(price: 15000);
await productProvider.updateProduct(updatedProduct);
```

##### `deleteProduct(int productId)`
상품을 삭제합니다.

```dart
Future<void> deleteProduct(int productId)
```

**파라미터**:
- `productId` (int): 삭제할 상품의 ID

**반환값**: `Future<void>`

**예외**:
- `NotFoundException`: 상품을 찾을 수 없음
- `ProviderException`: 데이터베이스 삭제 실패

**사용 예시**:
```dart
await productProvider.deleteProduct(123);
```

##### `searchProducts(String query)`
상품을 검색합니다.

```dart
Future<void> searchProducts(String query)
```

**파라미터**:
- `query` (String): 검색어

**반환값**: `Future<void>`

**사용 예시**:
```dart
await productProvider.searchProducts('커피');
```

##### `filterBySeller(String sellerName)`
판매자별로 상품을 필터링합니다.

```dart
Future<void> filterBySeller(String sellerName)
```

**파라미터**:
- `sellerName` (String): 판매자 이름

**반환값**: `Future<void>`

**사용 예시**:
```dart
await productProvider.filterBySeller('김판매');
```

##### `sortProducts(ProductSortOption sortOption)`
상품 목록을 정렬합니다.

```dart
Future<void> sortProducts(ProductSortOption sortOption)
```

**파라미터**:
- `sortOption` (ProductSortOption): 정렬 옵션

**반환값**: `Future<void>`

**사용 예시**:
```dart
await productProvider.sortProducts(ProductSortOption.priceAsc);
```

### SaleProvider

판매 관련 상태를 관리하는 Provider입니다.

#### 클래스 정보
```dart
class SaleProvider extends OptimizedProvider<SaleState>
```

#### 주요 메서드

##### `loadSales()`
판매 목록을 로드합니다.

```dart
Future<void> loadSales()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
final saleProvider = ref.read(saleProviderProvider.notifier);
await saleProvider.loadSales();
```

##### `addSale(Sale sale)`
새로운 판매를 등록합니다.

```dart
Future<void> addSale(Sale sale)
```

**파라미터**:
- `sale` (Sale): 등록할 판매 객체

**반환값**: `Future<void>`

**사용 예시**:
```dart
final sale = Sale(
  productId: 123,
  quantity: 2,
  totalAmount: 20000,
  sellerName: '김판매',
);
await saleProvider.addSale(sale);
```

##### `updateSale(Sale sale)`
기존 판매를 수정합니다.

```dart
Future<void> updateSale(Sale sale)
```

**파라미터**:
- `sale` (Sale): 수정할 판매 객체 (ID 필수)

**반환값**: `Future<void>`

**사용 예시**:
```dart
final updatedSale = sale.copyWith(quantity: 3);
await saleProvider.updateSale(updatedSale);
```

##### `deleteSale(int saleId)`
판매를 삭제합니다.

```dart
Future<void> deleteSale(int saleId)
```

**파라미터**:
- `saleId` (int): 삭제할 판매의 ID

**반환값**: `Future<void>`

**사용 예시**:
```dart
await saleProvider.deleteSale(456);
```

### PrepaymentProvider

선입금 관련 상태를 관리하는 Provider입니다.

#### 클래스 정보
```dart
class PrepaymentProvider extends OptimizedProvider<PrepaymentState>
```

#### 주요 메서드

##### `loadPrepayments()`
선입금 목록을 로드합니다.

```dart
Future<void> loadPrepayments()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
final prepaymentProvider = ref.read(prepaymentProviderProvider.notifier);
await prepaymentProvider.loadPrepayments();
```

##### `addPrepayment(Prepayment prepayment)`
새로운 선입금을 등록합니다.

```dart
Future<void> addPrepayment(Prepayment prepayment)
```

**파라미터**:
- `prepayment` (Prepayment): 등록할 선입금 객체

**반환값**: `Future<void>`

**사용 예시**:
```dart
final prepayment = Prepayment(
  buyerName: '김구매',
  amount: 50000,
  isReceived: false,
);
await prepaymentProvider.addPrepayment(prepayment);
```

##### `usePrepayment(int prepaymentId, int amount)`
선입금을 사용합니다.

```dart
Future<void> usePrepayment(int prepaymentId, int amount)
```

**파라미터**:
- `prepaymentId` (int): 사용할 선입금의 ID
- `amount` (int): 사용할 금액

**반환값**: `Future<void>`

**사용 예시**:
```dart
await prepaymentProvider.usePrepayment(789, 10000);
```

---

## 🗄️ Repository API

### ProductRepository

상품 데이터에 대한 CRUD 작업을 처리하는 Repository입니다.

#### 클래스 정보
```dart
class ProductRepository
```

#### 주요 메서드

##### `getAllProducts()`
모든 상품 목록을 조회합니다.

```dart
Future<List<Product>> getAllProducts()
```

**반환값**: `Future<List<Product>>` - 상품 목록

**예외**:
- `DatabaseException`: 데이터베이스 연결 실패

**사용 예시**:
```dart
final repository = ProductRepository(databaseService);
final products = await repository.getAllProducts();
```

##### `getProductsSorted(ProductSortOption sortOption)`
정렬된 상품 목록을 조회합니다.

```dart
Future<List<Product>> getProductsSorted(ProductSortOption sortOption)
```

**파라미터**:
- `sortOption` (ProductSortOption): 정렬 옵션

**반환값**: `Future<List<Product>>` - 정렬된 상품 목록

**사용 예시**:
```dart
final products = await repository.getProductsSorted(ProductSortOption.priceDesc);
```

##### `getProductById(int id)`
ID로 특정 상품을 조회합니다.

```dart
Future<Product?> getProductById(int id)
```

**파라미터**:
- `id` (int): 상품 ID

**반환값**: `Future<Product?>` - 상품 객체 또는 null

**사용 예시**:
```dart
final product = await repository.getProductById(123);
```

##### `getProductsBySeller(String sellerName)`
판매자별 상품 목록을 조회합니다.

```dart
Future<List<Product>> getProductsBySeller(String sellerName)
```

**파라미터**:
- `sellerName` (String): 판매자 이름

**반환값**: `Future<List<Product>>` - 해당 판매자의 상품 목록

**사용 예시**:
```dart
final products = await repository.getProductsBySeller('김판매');
```

##### `insertProduct(Product product)`
새로운 상품을 등록합니다.

```dart
Future<int> insertProduct(Product product)
```

**파라미터**:
- `product` (Product): 등록할 상품 객체

**반환값**: `Future<int>` - 새로 생성된 상품의 ID

**사용 예시**:
```dart
final newProduct = Product(name: '새 상품', price: 10000);
final productId = await repository.insertProduct(newProduct);
```

##### `updateProduct(Product product)`
기존 상품을 수정합니다.

```dart
Future<int> updateProduct(Product product)
```

**파라미터**:
- `product` (Product): 수정할 상품 객체 (ID 필수)

**반환값**: `Future<int>` - 수정된 레코드 수

**사용 예시**:
```dart
final updatedProduct = product.copyWith(price: 15000);
final affectedRows = await repository.updateProduct(updatedProduct);
```

##### `deleteProduct(int id)`
상품을 삭제합니다.

```dart
Future<int> deleteProduct(int id)
```

**파라미터**:
- `id` (int): 삭제할 상품의 ID

**반환값**: `Future<int>` - 삭제된 레코드 수

**사용 예시**:
```dart
final deletedRows = await repository.deleteProduct(123);
```

##### `searchProducts(String query)`
상품을 검색합니다.

```dart
Future<List<Product>> searchProducts(String query)
```

**파라미터**:
- `query` (String): 검색어

**반환값**: `Future<List<Product>>` - 검색 결과

**사용 예시**:
```dart
final results = await repository.searchProducts('커피');
```

### SaleRepository

판매 데이터에 대한 CRUD 작업을 처리하는 Repository입니다.

#### 클래스 정보
```dart
class SaleRepository
```

#### 주요 메서드

##### `getAllSales()`
모든 판매 목록을 조회합니다.

```dart
Future<List<Sale>> getAllSales()
```

**반환값**: `Future<List<Sale>>` - 판매 목록

**사용 예시**:
```dart
final repository = SaleRepository(databaseService);
final sales = await repository.getAllSales();
```

##### `getSaleById(int id)`
ID로 특정 판매를 조회합니다.

```dart
Future<Sale?> getSaleById(int id)
```

**파라미터**:
- `id` (int): 판매 ID

**반환값**: `Future<Sale?>` - 판매 객체 또는 null

**사용 예시**:
```dart
final sale = await repository.getSaleById(456);
```

##### `insertSale(Sale sale)`
새로운 판매를 등록합니다.

```dart
Future<int> insertSale(Sale sale)
```

**파라미터**:
- `sale` (Sale): 등록할 판매 객체

**반환값**: `Future<int>` - 새로 생성된 판매의 ID

**사용 예시**:
```dart
final newSale = Sale(productId: 123, quantity: 2, totalAmount: 20000);
final saleId = await repository.insertSale(newSale);
```

##### `updateSale(Sale sale)`
기존 판매를 수정합니다.

```dart
Future<int> updateSale(Sale sale)
```

**파라미터**:
- `sale` (Sale): 수정할 판매 객체 (ID 필수)

**반환값**: `Future<int>` - 수정된 레코드 수

**사용 예시**:
```dart
final updatedSale = sale.copyWith(quantity: 3);
final affectedRows = await repository.updateSale(updatedSale);
```

##### `deleteSale(int id)`
판매를 삭제합니다.

```dart
Future<int> deleteSale(int id)
```

**파라미터**:
- `id` (int): 삭제할 판매의 ID

**반환값**: `Future<int>` - 삭제된 레코드 수

**사용 예시**:
```dart
final deletedRows = await repository.deleteSale(456);
```

### PrepaymentRepository

선입금 데이터에 대한 CRUD 작업을 처리하는 Repository입니다.

#### 클래스 정보
```dart
class PrepaymentRepository
```

#### 주요 메서드

##### `getAllPrepayments()`
모든 선입금 목록을 조회합니다.

```dart
Future<List<Prepayment>> getAllPrepayments()
```

**반환값**: `Future<List<Prepayment>>` - 선입금 목록

**사용 예시**:
```dart
final repository = PrepaymentRepository(databaseService);
final prepayments = await repository.getAllPrepayments();
```

##### `getPrepaymentById(int id)`
ID로 특정 선입금을 조회합니다.

```dart
Future<Prepayment?> getPrepaymentById(int id)
```

**파라미터**:
- `id` (int): 선입금 ID

**반환값**: `Future<Prepayment?>` - 선입금 객체 또는 null

**사용 예시**:
```dart
final prepayment = await repository.getPrepaymentById(789);
```

##### `insertPrepayment(Prepayment prepayment)`
새로운 선입금을 등록합니다.

```dart
Future<int> insertPrepayment(Prepayment prepayment)
```

**파라미터**:
- `prepayment` (Prepayment): 등록할 선입금 객체

**반환값**: `Future<int>` - 새로 생성된 선입금의 ID

**사용 예시**:
```dart
final newPrepayment = Prepayment(buyerName: '김구매', amount: 50000);
final prepaymentId = await repository.insertPrepayment(newPrepayment);
```

##### `updatePrepayment(Prepayment prepayment)`
기존 선입금을 수정합니다.

```dart
Future<int> updatePrepayment(Prepayment prepayment)
```

**파라미터**:
- `prepayment` (Prepayment): 수정할 선입금 객체 (ID 필수)

**반환값**: `Future<int>` - 수정된 레코드 수

**사용 예시**:
```dart
final updatedPrepayment = prepayment.copyWith(isReceived: true);
final affectedRows = await repository.updatePrepayment(updatedPrepayment);
```

##### `deletePrepayment(int id)`
선입금을 삭제합니다.

```dart
Future<int> deletePrepayment(int id)
```

**파라미터**:
- `id` (int): 삭제할 선입금의 ID

**반환값**: `Future<int>` - 삭제된 레코드 수

**사용 예시**:
```dart
final deletedRows = await repository.deletePrepayment(789);
```

---

## 🔧 Service API

### DatabaseService

SQLite 데이터베이스를 관리하는 서비스입니다.

#### 클래스 정보
```dart
class DatabaseService
```

#### 주요 메서드

##### `initialize()`
데이터베이스를 초기화합니다.

```dart
Future<void> initialize()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
final databaseService = DatabaseService();
await databaseService.initialize();
```

##### `get database`
데이터베이스 인스턴스를 반환합니다.

```dart
Future<Database> get database
```

**반환값**: `Future<Database>` - SQLite 데이터베이스 인스턴스

**사용 예시**:
```dart
final db = await databaseService.database;
```

##### `close()`
데이터베이스 연결을 종료합니다.

```dart
Future<void> close()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
await databaseService.close();
```

### MemoryManager

메모리 사용량을 최적화하는 매니저입니다.

#### 클래스 정보
```dart
class MemoryManager
```

#### 주요 메서드

##### `initialize()`
메모리 매니저를 초기화합니다.

```dart
Future<void> initialize()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
final memoryManager = MemoryManager();
await memoryManager.initialize();
```

##### `getMemoryUsage()`
현재 메모리 사용량을 반환합니다.

```dart
MemoryUsage getMemoryUsage()
```

**반환값**: `MemoryUsage` - 메모리 사용량 정보

**사용 예시**:
```dart
final usage = memoryManager.getMemoryUsage();
print('Used: ${usage.used}, Available: ${usage.available}');
```

##### `optimizeMemory()`
메모리를 최적화합니다.

```dart
Future<void> optimizeMemory()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
await memoryManager.optimizeMemory();
```

### ErrorOptimizer

에러 처리 및 복구를 최적화하는 서비스입니다.

#### 클래스 정보
```dart
class ErrorOptimizer
```

#### 주요 메서드

##### `initialize()`
에러 최적화기를 초기화합니다.

```dart
Future<void> initialize()
```

**반환값**: `Future<void>`

**사용 예시**:
```dart
final errorOptimizer = ErrorOptimizer();
await errorOptimizer.initialize();
```

##### `handleError(dynamic error, StackTrace? stackTrace)`
에러를 처리합니다.

```dart
Future<void> handleError(dynamic error, StackTrace? stackTrace)
```

**파라미터**:
- `error` (dynamic): 발생한 에러
- `stackTrace` (StackTrace?): 스택 트레이스

**반환값**: `Future<void>`

**사용 예시**:
```dart
try {
  // 작업 수행
} catch (e, stackTrace) {
  await errorOptimizer.handleError(e, stackTrace);
}
```

---

## 🎯 Business Logic API

### Product CRUD Logic

상품 관련 비즈니스 로직을 처리하는 클래스입니다.

#### 클래스 정보
```dart
class ProductCrudLogic
```

#### 주요 메서드

##### `validateProduct(Product product)`
상품 데이터의 유효성을 검증합니다.

```dart
ValidationResult validateProduct(Product product)
```

**파라미터**:
- `product` (Product): 검증할 상품 객체

**반환값**: `ValidationResult` - 검증 결과

**사용 예시**:
```dart
final logic = ProductCrudLogic();
final result = logic.validateProduct(product);
if (!result.isValid) {
  print('Validation errors: ${result.errors}');
}
```

##### `calculateTotalValue(List<Product> products)`
상품 목록의 총 가치를 계산합니다.

```dart
int calculateTotalValue(List<Product> products)
```

**파라미터**:
- `products` (List<Product>): 상품 목록

**반환값**: `int` - 총 가치

**사용 예시**:
```dart
final totalValue = logic.calculateTotalValue(products);
print('Total value: $totalValue');
```

### Sale Business Logic

판매 관련 비즈니스 로직을 처리하는 클래스입니다.

#### 클래스 정보
```dart
class SaleBusinessLogic
```

#### 주요 메서드

##### `calculateSaleTotal(Sale sale)`
판매의 총 금액을 계산합니다.

```dart
int calculateSaleTotal(Sale sale)
```

**파라미터**:
- `sale` (Sale): 판매 객체

**반환값**: `int` - 총 금액

**사용 예시**:
```dart
final logic = SaleBusinessLogic();
final total = logic.calculateSaleTotal(sale);
```

##### `applyDiscount(int originalPrice, double discountRate)`
할인을 적용합니다.

```dart
int applyDiscount(int originalPrice, double discountRate)
```

**파라미터**:
- `originalPrice` (int): 원래 가격
- `discountRate` (double): 할인율 (0.0 ~ 1.0)

**반환값**: `int` - 할인 적용된 가격

**사용 예시**:
```dart
final discountedPrice = logic.applyDiscount(10000, 0.1); // 10% 할인
```

---

## 🔒 예외 처리

### ProviderException
Provider 계층에서 발생하는 예외입니다.

```dart
class ProviderException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
}
```

### DatabaseException
데이터베이스 작업 중 발생하는 예외입니다.

```dart
class DatabaseException implements Exception {
  final String message;
  final String? sql;
  final dynamic originalError;
}
```

### ValidationException
데이터 유효성 검증 실패 시 발생하는 예외입니다.

```dart
class ValidationException implements Exception {
  final String message;
  final List<String> errors;
  final Map<String, dynamic>? data;
}
```

---

## 📝 사용 예시

### 전체 워크플로우 예시

```dart
// 1. Provider를 통한 상품 등록
final productProvider = ref.read(productProviderProvider.notifier);

final newProduct = Product(
  name: '새 상품',
  price: 10000,
  quantity: 50,
  sellerName: '김판매',
);

try {
  await productProvider.addProduct(newProduct);
  print('상품이 성공적으로 등록되었습니다.');
} catch (e) {
  print('상품 등록 실패: $e');
}

// 2. Repository를 통한 직접 데이터 접근
final repository = ProductRepository(databaseService);
final products = await repository.getAllProducts();

// 3. 비즈니스 로직 처리
final logic = ProductCrudLogic();
final totalValue = logic.calculateTotalValue(products);
print('총 상품 가치: $totalValue');
```

---

**작성자**: Blue  
**버전**: 1.0.0  
**최종 업데이트**: 2025년 1월 