import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../widgets/onboarding_components.dart';

/// 비밀번호 찾기 화면
///
/// 이메일을 입력받아 비밀번호 재설정 메일을 발송하는 화면
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final emailController = TextEditingController();
  final FocusNode emailFocus = FocusNode();
  bool isLoading = false;
  String? error;
  String? successMessage;

  @override
  void initState() {
    super.initState();
    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();
  }

  @override
  void dispose() {
    emailController.dispose();
    emailFocus.dispose();
    super.dispose();
  }

  Future<void> _handleSendResetEmail() async {
    final email = emailController.text.trim();
    if (email.isEmpty) {
      setState(() {
        error = '이메일을 입력해 주세요.';
        successMessage = null;
      });
      return;
    }

    setState(() { 
      isLoading = true; 
      error = null; 
      successMessage = null; 
    });

    try {
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      setState(() {
        successMessage = '비밀번호 재설정 메일이 발송되었습니다.\n메일함을 확인해 주세요.';
      });
    } on FirebaseAuthException catch (e) {
      setState(() {
        error = _firebaseErrorToKorean(e);
      });
    } catch (e) {
      setState(() {
        error = '비밀번호 재설정 메일 발송 중 오류가 발생했습니다.';
      });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  String _firebaseErrorToKorean(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-email':
        return '올바른 이메일 형식이 아닙니다.';
      case 'user-not-found':
        return '해당 이메일로 가입된 계정이 없습니다.';
      case 'too-many-requests':
        return '잠시 후 다시 시도해 주세요.';
      case 'network-request-failed':
        return '네트워크 오류가 발생했습니다.';
      case 'user-disabled':
        return '이 계정은 비활성화되어 있습니다.';
      default:
        return '입력 정보를 다시 확인해 주세요.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.onboardingTextPrimary,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '비밀번호 찾기',
          style: TextStyle(
            color: AppColors.onboardingTextPrimary,
            fontSize: ResponsiveHelper.getTitleFontSize(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: ResponsiveHelper.getScreenPadding(context),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: ResponsiveHelper.getCardMaxWidth(context),
                ),
                child: _buildForm(context),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Container(
      padding: ResponsiveHelper.getCardPadding(context),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 아이콘
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
              boxShadow: [
                BoxShadow(
                  color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Icon(
              Icons.lock_reset,
              size: 36,
              color: Colors.white,
            ),
          ),

          OnboardingComponents.buildSectionSpacing(context),

          // 제목
          Text(
            '비밀번호를 잊으셨나요?',
            style: TextStyle(
              fontSize: ResponsiveHelper.getTitleFontSize(context),
              fontWeight: FontWeight.w700,
              color: AppColors.onboardingTextPrimary,
            ),
            textAlign: TextAlign.center,
          ),

          OnboardingComponents.buildSmallSpacing(context),

          // 설명
          Text(
            '가입하신 이메일 주소를 입력해 주세요.\n비밀번호 재설정 링크를 보내드립니다.',
            style: TextStyle(
              fontSize: ResponsiveHelper.getBodyFontSize(context),
              color: AppColors.onboardingTextSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          OnboardingComponents.buildSectionSpacing(context),

          // 이메일 입력
          OnboardingComponents.buildTextField(
            context: context,
            controller: emailController,
            focusNode: emailFocus,
            label: '이메일',
            prefixIcon: Icons.email_outlined,
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.emailAddress,
            onSubmitted: (_) => _handleSendResetEmail(),
          ),

          OnboardingComponents.buildSmallSpacing(context),

          // 메시지 표시
          _buildMessages(context),

          OnboardingComponents.buildSmallSpacing(context),

          // 전송 버튼
          OnboardingComponents.buildPrimaryButton(
            context: context,
            text: '재설정 링크 보내기',
            onPressed: _handleSendResetEmail,
            isLoading: isLoading,
            icon: Icons.send,
          ),

          OnboardingComponents.buildSmallSpacing(context),

          // 로그인으로 돌아가기
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 8),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              '로그인으로 돌아가기',
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context),
                color: AppColors.onboardingTextSecondary.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessages(BuildContext context) {
    if (error == null && successMessage == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: ResponsiveHelper.getCardPadding(context),
      decoration: BoxDecoration(
        color: error != null 
          ? AppColors.error.withValues(alpha: 0.1)
          : AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context) * 0.75),
        border: Border.all(
          color: error != null 
            ? AppColors.error.withValues(alpha: 0.3)
            : AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            error != null ? Icons.error_outline : Icons.check_circle_outline,
            color: error != null ? AppColors.error : AppColors.success,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error ?? successMessage!,
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context) * 0.9,
                color: error != null ? AppColors.error : AppColors.success,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
