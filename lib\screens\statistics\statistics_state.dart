import '../../models/transaction_type.dart';

/// 통계 데이터 클래스들
///
/// 판매 통계 데이터를 구조화하여 관리하는 클래스들입니다.
/// - 거래 유형별 통계
/// - 상품별 통계
/// - 판매자별 통계
/// - 집계 및 분석 기능

/// 판매 통계 데이터 클래스
///
/// 전체 판매 통계와 세부 통계를 관리합니다.
/// - 전체 거래 수, 수량, 금액
/// - 거래 유형별 통계
/// - 상품별 통계
/// - 판매자별 통계
class SalesStats {
  /// 전체 거래 수
  int totalTransactions = 0;

  /// 전체 판매 수량
  int totalQuantity = 0;

  /// 전체 판매 금액
  int totalAmount = 0;

  /// 거래 유형별 통계 맵
  Map<TransactionType, TransactionTypeStats> transactionTypeStats = {};

  /// 상품별 통계 맵
  Map<String, ProductStats> productStats = {};

  /// 판매자별 통계 맵
  Map<String, SellerStats> sellerStats = {};
}

/// 거래 유형별 통계 클래스
///
/// 특정 거래 유형(판매, 할인, 서비스 등)의 통계를 관리합니다.
/// - 거래 수, 수량, 금액 집계
/// - 거래 유형별 분석 지원
class TransactionTypeStats {
  /// 거래 수
  int count = 0;

  /// 판매 수량
  int quantity = 0;

  /// 판매 금액
  int amount = 0;
}

/// 상품별 통계 클래스
///
/// 특정 상품의 판매 통계를 관리합니다.
/// - 상품명 기반 통계
/// - 거래 수, 수량, 금액 집계
/// - 상품별 성과 분석 지원
class ProductStats {
  /// 상품명
  final String productName;

  /// 거래 수
  int count = 0;

  /// 판매 수량
  int quantity = 0;

  /// 판매 금액
  int amount = 0;

  /// 상품별 통계 생성
  ///
  /// [productName]: 상품명
  ProductStats({required this.productName});
}

/// 판매자별 통계 클래스
///
/// 특정 판매자의 판매 통계를 관리합니다.
/// - 판매자명 기반 통계
/// - 거래 수, 수량, 금액 집계
/// - 판매자별 성과 분석 지원
class SellerStats {
  /// 판매자명
  final String sellerName;

  /// 거래 수
  int count = 0;

  /// 판매 수량
  int quantity = 0;

  /// 판매 금액
  int amount = 0;

  /// 판매자별 통계 생성
  ///
  /// [sellerName]: 판매자명
  SellerStats({required this.sellerName});
} 