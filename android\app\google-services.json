{"project_info": {"project_number": "699872938105", "project_id": "parabara-1a504", "storage_bucket": "parabara-1a504.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:699872938105:android:64a0052ecfc10797f3aca8", "android_client_info": {"package_name": "com.blue.parabara"}}, "oauth_client": [{"client_id": "699872938105-3nrghoifs1inem7m41dvup8kftuk268j.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.blue.parabara", "certificate_hash": "66ada58f2a0d1803e12fbb839beaa18a3e92caf4"}}, {"client_id": "699872938105-0kgl0snkmk62ecrfkmoka6jq3dscj4he.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAbs9Ai6rQFGsvmGkPvdaWXHSZq2y4c4Yc"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "699872938105-0kgl0snkmk62ecrfkmoka6jq3dscj4he.apps.googleusercontent.com", "client_type": 3}, {"client_id": "699872938105-ktp9m6tj5tf02mrs30dqstiai6bao6sg.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.blue.parabara"}}]}}}], "configuration_version": "1"}