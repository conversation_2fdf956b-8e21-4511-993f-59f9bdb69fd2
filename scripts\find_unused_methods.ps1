<#
  Detect potentially unused Dart methods/functions.
  Heuristic (may produce false positives/negatives):
    - Finds top-level functions and class/extension methods defined with a body `{` or arrow `=>`.
    - Counts textual references (identifier + `(`) across lib/ excluding its own definition.
    - Excludes common framework / lifecycle / data methods (build, initState, dispose, etc.)
    - Excludes overrides annotated with @override.
    - Excludes constructors (name == class) and factory constructors.
    - Private members (starting with `_`) are still considered; pass -IncludePrivate:$false to drop them.
  Usage:
    pwsh ./scripts/find_unused_methods.ps1 -Output unused_methods_report.json
  Output JSON fields:
    Name, File, Line, Kind, Private, RefCount (occurrences incl. definition), Unused (RefCount==1), Reason(optional)
#>
param(
  [string]$Root = 'lib',
  [string]$Output = 'unused_methods_report.json',
  [switch]$IncludePrivate,             # include private methods starting with _
  [switch]$ExcludeGenerated,           # exclude *.freezed.dart, *.g.dart, *.chopper.dart
  [switch]$OnlyRefCountZero             # restrict output to RefCount == 0 (definition never invoked)
)

Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

if(-not (Test-Path $Root)) { throw "Root path '$Root' not found." }

$dartFiles = Get-ChildItem -Path $Root -Recurse -Filter *.dart
if($ExcludeGenerated){
  $dartFiles = $dartFiles | Where-Object { $_.Name -notmatch '\.(freezed|g|chopper)\.dart$' }
}

# Collect file contents once
$fileContents = @{}
foreach($f in $dartFiles){ $fileContents[$f.FullName] = Get-Content -Raw -Path $f.FullName }

# Collect class / extension / mixin names per file
$classNames = [System.Collections.Generic.HashSet[string]]::new()
foreach($f in $dartFiles){
  if($fileContents[$f.FullName] -match '(?m)^\s*(class|extension|mixin)\s+([A-Za-z_][A-Za-z0-9_]*)'){
    $matchesAll = [regex]::Matches($fileContents[$f.FullName], '(?m)^\s*(class|extension|mixin)\s+([A-Za-z_][A-Za-z0-9_]*)')
    foreach($m in $matchesAll){ [void]$classNames.Add($m.Groups[2].Value) }
  }
}

# Regex patterns
<#
  New stricter pattern:
   - Requires identifier followed by (...)
   - Line must end with '{' or '=>' (after optional async)
   - Avoids accidental capture of parts of 'abstract class' etc.
#>
$methodPattern = '(?m)^(?<indent>\s*)(?<decor>(?:@\w+\s*)*)(?<mods>(?:abstract|static|final|const|late|async|sync|external|override)\s+)*(?<ret>[A-Za-z_][A-Za-z0-9_<>,?\[\]\.? ]+\s+)?(?<name>[A-Za-z_][A-Za-z0-9_]*)\s*\([^;]*\)\s*(?:async\*?|sync\*?)?\s*(?:=>|\{)'
$control = 'if|for|while|switch|return|assert|with|case|default|else|catch|on|class|typedef|extension|mixin|enum'

$arrowPattern = '(?m)^(?<indent>\s*)(?<decor>(?:@\w+\s*)*)(?<mods>(?:abstract|static|final|const|late|async|sync|external|override)\s+)*(?<ret>[A-Za-z_][A-Za-z0-9_<>,?\[\]\.? ]+\s+)?(?<name>[A-Za-z_][A-Za-z0-9_]*)\s*\([^;]*\)\s*=>'

$candidates = @()

foreach($f in $dartFiles){
  $content = $fileContents[$f.FullName]
  $lines = $content -split "`n"
  $inExtension = $false
  foreach($idx in 0..($lines.Length-1)){
    $line = $lines[$idx]
    if($line -match '^\s*extension\b'){ $inExtension = $true }
    if($line -match '^\s*}\s*$'){ $inExtension = $false }
    if($line -match $methodPattern){
      $name = $Matches['name']
      if(-not $name){ continue }
      if($line -match "^\s*(?:$control)\b") { continue }
      if($line -match '^\s*factory\b'){ continue }
      # Skip constructor: name same as a class and signature not operator
      if($classNames.Contains($name) -and $line -notmatch '\boperator\b'){ continue }
      $isPrivate = $name.StartsWith('_')
      if(-not $IncludePrivate -and $isPrivate){ continue }
      if($name -in @('build','initState','dispose','didChangeDependencies','didUpdateWidget','createState','toJson','fromJson','copyWith','debugFillProperties','hashCode','==','noSuchMethod','toString')){ continue }
      $prevLine = if($idx -gt 0) { $lines[$idx-1] } else { '' }
      if($prevLine -match '@override'){ continue }
      $kind = if($inExtension) { 'extension_method' } else { 'method' }
      $signature = $line.Trim()
      $candidates += [pscustomobject]@{
        Name=$name; File=$f.FullName; Line=($idx+1); Private=$isPrivate; Kind=$kind; Signature=$signature
      }
    }
  }
}

$candidates = $candidates | Sort-Object File, Line -Unique
$allContent = ($fileContents.Values -join "`n")
Write-Host "Scanning reference counts for $($candidates.Count) candidate methods..." -ForegroundColor Cyan

$results = foreach($c in $candidates){
  $pattern = "\b$([regex]::Escape($c.Name))\s*\("  # naive invocation pattern
  $count = ([regex]::Matches($allContent, $pattern)).Count
  $unused = $count -le 1
  [pscustomobject]@{
    Name=$c.Name;
    File=$c.File.Replace((Resolve-Path .).Path + '\\','');
    Line=$c.Line;
    Private=$c.Private;
    Kind=$c.Kind;
    RefCount=$count;
    Unused=$unused;
    Signature=$c.Signature;
  }
}

$filtered = $results | Where-Object Unused
if($OnlyRefCountZero){
  $filtered = $filtered | Where-Object { $_.RefCount -eq 0 }
}
$unusedList = $filtered | Sort-Object RefCount, Name
if(-not $unusedList){ $unusedList = @() }

# Debug: show object types
Write-Host "DEBUG results type: $($results.GetType().FullName) count=$([int]($results | Measure-Object).Count)" -ForegroundColor DarkGray
$ulCount = ($unusedList | Measure-Object).Count
Write-Host "DEBUG unusedList type: $($unusedList.GetType().FullName) count=$ulCount" -ForegroundColor DarkGray

$outObj = [pscustomobject]@{
  Generated = (Get-Date).ToString('s');
  Root = $Root;
  TotalCandidates = $results.Count;
  UnusedCount = $unusedList.Count;
  IncludePrivate = [bool]$IncludePrivate;
  ExcludeGenerated = [bool]$ExcludeGenerated;
  OnlyRefCountZero = [bool]$OnlyRefCountZero;
  Notes = 'Heuristic. Verify manually before deletion. RefCount counts textual occurrences including definition.';
  Items = $unusedList
}

$json = $outObj | ConvertTo-Json -Depth 5
Set-Content -Path $Output -Value $json -Encoding UTF8
Write-Host "Report written to $Output" -ForegroundColor Green
Write-Host "Potential unused methods: $($unusedList.Count) / $($results.Count)" -ForegroundColor Yellow
