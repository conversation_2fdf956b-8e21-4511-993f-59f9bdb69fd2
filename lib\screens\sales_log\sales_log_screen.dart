import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/sales_log_provider.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import '../../utils/currency_utils.dart';
import '../../utils/date_utils.dart' as app_date_utils;

import '../statistics/statistics_screen.dart';
import '../settings/settings_screen.dart';
import '../../utils/logger_utils.dart';

import '../../utils/toast_utils.dart';

/// 원본 SalesLogActivity와 동일한 단순 판매 기록 화면
class SalesLogScreen extends ConsumerStatefulWidget {
  const SalesLogScreen({super.key});

  @override
  ConsumerState<SalesLogScreen> createState() => _SalesLogScreenState();
}

class _SalesLogScreenState extends ConsumerState<SalesLogScreen>
    with RestorationMixin {
  static const String _tag = 'SalesLogScreen';
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;

  @override
  String? get restorationId => 'sales_log_screen';

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
    _scrollController.addListener(_onScroll);
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 화면 복귀 시 자동 새로고침
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _refreshData();
      }
    });
  }

  @override
  void didUpdateWidget(SalesLogScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 위젯 업데이트 시 데이터 새로고침
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _refreshData();
      }
    });
  }

  /// 화면 복귀/앱 재시작 시 데이터 새로고침
  Future<void> _refreshData() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_refreshData', tag: _tag);

    try {
      if (mounted) {
        // 단순화된 데이터 갱신
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      }
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '데이터 새로고침 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'operation': 'refresh'},
      );
    }

    LoggerUtils.methodEnd('_refreshData', tag: _tag);
  }

  @override
  void dispose() {
    LoggerUtils.methodStart('dispose', tag: _tag);
    _scrollController.dispose();
    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent * 0.8 &&
        !_isLoading) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_loadInitialData', tag: _tag);
    setState(() => _isLoading = true);

    final startTime = DateTime.now();

    try {
      if (mounted) {
        // 단순화된 데이터 갱신
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

        // 실시간 데이터 감시를 위해 ref.watch 사용
        final salesLogsCount = ref.watch(salesLogsProvider).length;
        LoggerUtils.logPerformance(
          'sales_logs_initial_load',
          DateTime.now().difference(startTime),
          metrics: {
            'success': true,
            'count': salesLogsCount,
          },
        );
      }
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '초기 데이터 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'operation': 'initial_load'},
      );

      if (mounted) {
        ToastUtils.showError(context, '초기 데이터를 불러오는 중 오류가 발생했습니다');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }

    LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
  }

  Future<void> _loadMoreData() async {
    if (!mounted || _isLoading) return;

    // 실시간 데이터 감시를 위해 ref.watch 사용
    final currentCount = ref.watch(salesLogsProvider).length;
    LoggerUtils.methodStart(
      '_loadMoreData',
      tag: _tag,
      data: {'current_count': currentCount},
    );

    setState(() => _isLoading = true);
    final startTime = DateTime.now();

    try {
      if (mounted) {
        // 실시간 데이터 감시를 위해 ref.watch 사용
        final previousCount = ref.watch(salesLogsProvider).length;
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
        final newCount = ref.watch(salesLogsProvider).length;

        LoggerUtils.logPerformance(
          'sales_logs_load_more',
          DateTime.now().difference(startTime),
          metrics: {
            'success': true,
            'previous_count': previousCount,
            'new_count': newCount,
            'loaded_count': newCount - previousCount,
          },
        );
      }
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '추가 데이터 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'operation': 'load_more'},
      );

      if (mounted) {
        ToastUtils.showError(context, '추가 데이터를 불러오는 중 오류가 발생했습니다');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }

    LoggerUtils.methodEnd('_loadMoreData', tag: _tag);
  }

  @override
  Widget build(BuildContext context) {
    LoggerUtils.methodStart('build', tag: _tag);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        title: Text(
          '판매 기록',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onPrimary,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics_outlined),
            onPressed: _navigateToStatistics,
            tooltip: '통계',
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: _navigateToSettings,
            tooltip: '설정',
          ),
        ],
      ),
      body: SafeArea(
        child: Consumer(
          builder: (context, ref, child) {
            // 실시간 상태 감시를 위한 watch 호출
            final salesLogs = ref.watch(salesLogsProvider);
            final isLoading = ref.watch(salesLogIsLoadingProvider);
            final errorMessage = ref.watch(salesLogErrorMessageProvider);

            // 디버깅을 위한 로그
            LoggerUtils.logDebug(
              'SalesLogScreen - 현재 판매 로그 수: ${salesLogs.length}',
              tag: 'SalesLogScreen',
            );
            LoggerUtils.logDebug(
              'SalesLogScreen - 로딩 상태: $isLoading',
              tag: 'SalesLogScreen',
            );
            LoggerUtils.logDebug(
              'SalesLogScreen - 에러 메시지: $errorMessage',
              tag: 'SalesLogScreen',
            );

            if (isLoading && salesLogs.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            if (errorMessage != null && salesLogs.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '오류가 발생했습니다',
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                        fontFamily: 'Pretendard',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      errorMessage,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontFamily: 'Pretendard',
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadInitialData,
                      child: const Text('다시 시도'),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: _loadInitialData,
              child: RepaintBoundary(
                child: ListView.separated(
                  controller: _scrollController,
                  restorationId: 'sales_log_scroll',
                  padding: const EdgeInsets.all(16),
                  itemCount: salesLogs.length + (_isLoading ? 1 : 0),
                  cacheExtent: 800, // 캐시 범위 확대로 스크롤 성능 개선
                  addRepaintBoundaries: true, // RepaintBoundary 활성화
                  addAutomaticKeepAlives: false, // 메모리 최적화
                  separatorBuilder: (context, index) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    if (index == salesLogs.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final salesLog = salesLogs[index];
                    return RepaintBoundary(
                      key: ValueKey('sales_log_${salesLog.id}'), // 위젯 재사용을 위한 키
                      child: _buildSalesLogItem(salesLog),
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSalesLogItem(SalesLog salesLog) {
    return InkWell(
      onTap: () {
        LoggerUtils.logUserAction(
          'view_sales_log_detail',
          'SalesLogScreen',
          actionData: {
            'sales_log_id': salesLog.id,
            'product_name': salesLog.productName,
            'transaction_type': salesLog.transactionType.toString(),
          },
        );
        _showSalesLogActionDialog(salesLog);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    salesLog.productName,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  CurrencyUtils.formatCurrency(salesLog.totalAmount),
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontFamily: 'Pretendard',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  salesLog.sellerName ?? '판매자 없음',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                Text(
                  app_date_utils.DateUtils.formatKorDateTime(
                    DateTime.fromMillisecondsSinceEpoch(salesLog.saleTimestamp),
                  ),
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getTransactionTypeColor(salesLog.transactionType),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getTransactionTypeText(salesLog.transactionType),
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSalesLogActionDialog(SalesLog salesLog) {
    LoggerUtils.methodStart(
      '_showSalesLogActionDialog',
      tag: _tag,
      data: {'sales_log_id': salesLog.id, 'product_name': salesLog.productName},
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
          title: Text(
            salesLog.productName,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 20, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('판매자: ${salesLog.sellerName ?? "판매자 없음"}'),
              Text('수량: ${salesLog.soldQuantity}개'),
              Text('단가: ${CurrencyUtils.formatCurrency(salesLog.soldPrice)}'),
              Text(
                '판매 금액: ${CurrencyUtils.formatCurrency(salesLog.totalAmount)}',
              ),
              if (salesLog.paymentMethod != null)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text('결제수단: ${salesLog.paymentMethod}'),
                ),
              // 세트 할인 정보 (있는 경우에만 표시)
              if (salesLog.setDiscountAmount > 0 && salesLog.setDiscountNames != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.local_offer,
                            size: 16,
                            color: Colors.green.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '세트 할인 적용',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '세트명: ${salesLog.setDiscountNames}',
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                      Text(
                        '할인 금액: -${CurrencyUtils.formatCurrency(salesLog.setDiscountAmount)}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '원래 금액: ${CurrencyUtils.formatCurrency(salesLog.totalAmount + salesLog.setDiscountAmount)}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Text(
                '판매일: ${app_date_utils.DateUtils.formatKorDateTime(DateTime.fromMillisecondsSinceEpoch(salesLog.saleTimestamp))}',
              ),
              Text(
                '거래 유형: ${_getTransactionTypeText(salesLog.transactionType)}',
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                LoggerUtils.logUserAction(
                  'close_sales_log_detail',
                  'SalesLogScreen',
                  actionData: {'sales_log_id': salesLog.id},
                );
                Navigator.pop(context);
              },
              child: const Text('닫기'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deleteSalesLog(salesLog);
              },
              child: const Text('삭제', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
    );

    LoggerUtils.methodEnd('_showSalesLogActionDialog', tag: _tag);
  }

  Future<void> _deleteSalesLog(SalesLog salesLog) async {
    if (!mounted) return;

    LoggerUtils.methodStart(
      '_deleteSalesLog',
      tag: _tag,
      data: {'sales_log_id': salesLog.id, 'product_name': salesLog.productName},
    );

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('판매 기록 삭제'),
        content: Text('${salesLog.productName} 판매 기록을 삭제하시겠습니까?'),
        actions: [
          TextButton(
            onPressed: () {
              LoggerUtils.logUserAction(
                'cancel_delete_sales_log',
                'SalesLogScreen',
                actionData: {'sales_log_id': salesLog.id},
              );
              Navigator.pop(context, false);
            },
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () {
              LoggerUtils.logUserAction(
                'confirm_delete_sales_log',
                'SalesLogScreen',
                actionData: {'sales_log_id': salesLog.id},
              );
              Navigator.pop(context, true);
            },
            child: const Text('삭제', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final startTime = DateTime.now();
      try {
        // 완전한 삭제 메서드 사용 (재고 복구 + Firebase 동기화 모두 포함)
        final resultMessage = await ref
            .read(salesLogNotifierProvider.notifier)
            .deleteSalesLogComplete(salesLog);

        LoggerUtils.logBusinessEvent(
          'sales_log_deleted',
          'sales',
          eventData: {
            'sales_log_id': salesLog.id,
            'product_name': salesLog.productName,
            'transaction_type': salesLog.transactionType.toString(),
            'amount': salesLog.totalAmount,
          },
        );

        LoggerUtils.logPerformance(
          'delete_sales_log',
          DateTime.now().difference(startTime),
          metrics: {'success': true, 'sales_log_id': salesLog.id},
        );

        if (mounted) {
          ToastUtils.showSuccess(context, resultMessage);
        }
      } catch (e, stackTrace) {
        LoggerUtils.error(
          '판매 기록 삭제 실패',
          tag: _tag,
          error: e,
          stackTrace: stackTrace,
          data: {'sales_log_id': salesLog.id, 'operation': 'delete'},
        );

        if (mounted) {
          ToastUtils.showError(context, '판매 기록을 삭제하는 중 오류가 발생했습니다');
        }
      }
    }

    LoggerUtils.methodEnd('_deleteSalesLog', tag: _tag);
  }

  Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Colors.blue;
      case TransactionType.service:
        return Colors.green;
      case TransactionType.discount:
        return Colors.orange;

      case TransactionType.setDiscount:
        return Colors.green.shade600;
    }
  }

  /// 거래 타입별 텍스트
  String _getTransactionTypeText(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return '판매';
      case TransactionType.service:
        return '서비스';
      case TransactionType.discount:
        return '할인';

      case TransactionType.setDiscount:
        return '세트 할인';
    }
  }

  void _navigateToStatistics() {
    LoggerUtils.logUserAction('navigate_to_statistics', 'SalesLogScreen');

    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const StatisticsScreen()));
  }

  // 필터 관련 메서드들 제거됨 - 기존 필터 버튼을 수정할 예정

  void _navigateToSettings() {
    LoggerUtils.logUserAction('navigate_to_settings', 'SalesLogScreen');

    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));
  }

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}
}


