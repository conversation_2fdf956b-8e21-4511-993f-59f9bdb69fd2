import 'package:flutter/material.dart';

/// 열 수에 따라 반응형으로 텍스트 크기와 공간을 조정하는 유틸리티
/// 모든 디바이스(모바일, 타블렛, 데스크톱)에서 작동
class ResponsiveTextUtils {
  /// 열 수에 따른 상품명 텍스트 크기 계산
  /// 열 수가 많을수록 작아지고, 적을수록 커짐
  static double getProductNameFontSize(int columns) {
    // 기본값: 4열 기준 14pt (기존 12pt에서 2pt 증가)
    const double baseFontSize = 14.0;
    const int baseColumns = 4;
    
    // 열 수 차이에 따른 크기 조정
    final columnDiff = baseColumns - columns;
    final fontSizeAdjustment = columnDiff * 0.5; // 열당 0.5pt씩 조정
    
    // 최소/최대 크기 제한
    final fontSize = baseFontSize + fontSizeAdjustment;
    return fontSize.clamp(10.0, 20.0); // 최소값도 2pt 증가
  }

  /// 열 수에 따른 가격 텍스트 크기 계산
  static double getPriceFontSize(int columns) {
    const double baseFontSize = 13.0; // 기존 11pt에서 2pt 증가
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final fontSizeAdjustment = columnDiff * 0.4;
    
    final fontSize = baseFontSize + fontSizeAdjustment;
    return fontSize.clamp(9.0, 18.0); // 최소값도 2pt 증가
  }

  /// 열 수에 따른 수량 텍스트 크기 계산
  static double getQuantityFontSize(int columns) {
    const double baseFontSize = 13.0; // 기존 11pt에서 2pt 증가
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final fontSizeAdjustment = columnDiff * 0.4;
    
    final fontSize = baseFontSize + fontSizeAdjustment;
    return fontSize.clamp(9.0, 18.0); // 최소값도 2pt 증가
  }

  /// 열 수에 따른 텍스트 영역 높이 계산 (재고현황용 - 2줄 보장)
  static double getProductNameHeight(int columns) {
    // 보수적인 접근으로 충분한 높이 확보
    switch (columns) {
      case 1: return 52.0; // 1열: 충분한 높이
      case 2: return 48.0; // 2열: 모바일 기본
      case 3: return 44.0; // 3열: 태블릿
      case 4: return 40.0; // 4열: 데스크톱
      case 5: return 38.0; // 5열: 좁은 화면
      case 6: return 36.0; // 6열: 더 좁은 화면
      case 7: return 34.0; // 7열: 매우 좁은 화면
      case 8: return 32.0; // 8열: 극도로 좁은 화면
      default: return 40.0; // 기본값
    }
  }

  /// 열 수에 따른 1줄 텍스트 높이 계산 (재고현황용)
  static double getProductNameSingleLineHeight(int columns) {
    const double baseHeight = 24.0; // 1줄 기준 높이
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final heightAdjustment = columnDiff * 1.0; // 열당 1px씩 조정
    
    final height = baseHeight + heightAdjustment;
    return height.clamp(16.0, 32.0); // 최소 16px, 최대 32px
  }

  /// 열 수에 따른 텍스트 영역 높이 계산 (판매용 - 1줄)
  static double getSaleProductNameHeight(int columns) {
    const double baseHeight = 24.0; // 1줄 기준 높이
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final heightAdjustment = columnDiff * 1.5;
    
    final height = baseHeight + heightAdjustment;
    return height.clamp(16.0, 32.0);
  }

  /// 열 수에 따른 텍스트 패딩 계산
  static EdgeInsets getTextPadding(int columns) {
    const EdgeInsets basePadding = EdgeInsets.symmetric(horizontal: 6, vertical: 3);
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final paddingAdjustment = columnDiff * 0.5;
    
    final horizontalPadding = (basePadding.horizontal + paddingAdjustment).clamp(2.0, 12.0);
    final verticalPadding = (basePadding.vertical + paddingAdjustment).clamp(1.0, 8.0);
    
    return EdgeInsets.symmetric(
      horizontal: horizontalPadding,
      vertical: verticalPadding,
    );
  }

  /// 열 수에 따른 상품명 텍스트 스타일 (재고현황용)
  static TextStyle getProductNameTextStyle(int columns, {bool isOutOfStock = false}) {
    return TextStyle(
      fontSize: getProductNameFontSize(columns),
      fontWeight: FontWeight.w500,
      color: isOutOfStock ? const Color(0xFF888888) : const Color(0xFF000000),
      fontFamily: 'Pretendard',
    );
  }

  /// 열 수에 따른 상품명 텍스트 스타일 (판매용)
  static TextStyle getSaleProductNameTextStyle(int columns) {
    return TextStyle(
      fontSize: getProductNameFontSize(columns),
      fontWeight: FontWeight.bold,
      fontFamily: 'Pretendard',
    );
  }

  /// 열 수에 따른 가격 텍스트 스타일
  static TextStyle getPriceTextStyle(int columns, {bool isOutOfStock = false}) {
    return TextStyle(
      fontSize: getPriceFontSize(columns),
      color: isOutOfStock ? const Color(0xFF888888) : const Color(0xFF444444),
      fontFamily: 'Pretendard',
    );
  }

  /// 열 수에 따른 수량 텍스트 스타일
  static TextStyle getQuantityTextStyle(int columns, {bool isOutOfStock = false}) {
    return TextStyle(
      fontSize: getQuantityFontSize(columns),
      color: isOutOfStock ? const Color(0xFF888888) : const Color(0xFF444444),
    );
  }

  /// 열 수에 따른 카드 내부 패딩 계산
  static EdgeInsets getCardPadding(int columns) {
    const EdgeInsets basePadding = EdgeInsets.all(4);
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final paddingAdjustment = columnDiff * 0.5;
    
    final padding = (basePadding.left + paddingAdjustment).clamp(2.0, 8.0);
    return EdgeInsets.all(padding);
  }

  /// 열 수에 따른 아이템 간격 계산
  static double getItemSpacing(int columns) {
    const double baseSpacing = 4.0;
    const int baseColumns = 4;
    
    final columnDiff = baseColumns - columns;
    final spacingAdjustment = columnDiff * 0.5;
    
    final spacing = baseSpacing + spacingAdjustment;
    return spacing.clamp(2.0, 8.0);
  }
} 