# Firebase 관련 로그 최적화
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
}

# HTTP 로그 최적화
-assumenosideeffects class java.io.PrintStream {
    public void println(...);
    public void print(...);
}

# Firebase Storage 로그 최적화
-keep class com.google.firebase.storage.** { *; }
-dontwarn com.google.firebase.storage.**

# 메모리 최적화 - 사용하지 않는 코드 제거
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
    static void checkNotNullParameter(java.lang.Object, java.lang.String);
}

# 추가 최적화 설정
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
