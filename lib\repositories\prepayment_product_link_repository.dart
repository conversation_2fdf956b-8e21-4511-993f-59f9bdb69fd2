import 'package:sqflite/sqflite.dart';
import '../models/prepayment_product_link.dart';
import '../services/database_service.dart';

class PrepaymentProductLinkRepository {
  final DatabaseService _databaseService;
  late final Future<Database> _database;

  PrepaymentProductLinkRepository({required DatabaseService database})
      : _databaseService = database {
    _database = _databaseService.database;
  }

  Future<void> createTable() async {
    final db = await _database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS prepayment_product_link (
        virtualProductId INTEGER NOT NULL,
        productId INTEGER NOT NULL,
        linkedAt TEXT NOT NULL,
        quantity INTEGER DEFAULT 1,
        eventId INTEGER NOT NULL DEFAULT 1,
        PRIMARY KEY (virtualProductId, productId, eventId),
        FOREIGN KEY (virtualProductId) REFERENCES prepayment_virtual_product(id) ON DELETE CASCADE,
        <PERSON>OR<PERSON><PERSON>N KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (eventId) REFERENCES events(id) ON DELETE CASCADE
      )
    ''');

    // 기존 테이블에 eventId 컬럼이 없으면 추가
    try {
      await db.execute('ALTER TABLE prepayment_product_link ADD COLUMN eventId INTEGER DEFAULT 1');
    } catch (e) {
      // 컬럼이 이미 존재하면 무시
    }
  }

  Future<int> insertLink(PrepaymentProductLink link) async {
    final db = await _database;
    return await db.insert(
      'prepayment_product_link',
      link.toDatabaseMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<int> deleteLink(int virtualProductId, int productId, int eventId) async {
    final db = await _database;
    return await db.delete(
      'prepayment_product_link',
      where: 'virtualProductId = ? AND productId = ? AND eventId = ?',
      whereArgs: [virtualProductId, productId, eventId],
    );
  }

  Future<List<PrepaymentProductLink>> getLinksByVirtualProductId(int virtualProductId, int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      'prepayment_product_link',
      where: 'virtualProductId = ? AND eventId = ?',
      whereArgs: [virtualProductId, eventId],
    );
    return maps.map((map) => PrepaymentProductLink.fromMap(map)).toList();
  }

  Future<List<PrepaymentProductLink>> getLinksByProductId(int productId, int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      'prepayment_product_link',
      where: 'productId = ? AND eventId = ?',
      whereArgs: [productId, eventId],
    );
    return maps.map((map) => PrepaymentProductLink.fromMap(map)).toList();
  }

  Future<List<PrepaymentProductLink>> getAllLinksByEventId(int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      'prepayment_product_link',
      where: 'eventId = ?',
      whereArgs: [eventId],
    );
    return maps.map((map) => PrepaymentProductLink.fromMap(map)).toList();
  }

  // 하위 호환성을 위한 메서드 (기본 eventId = 1)
  Future<List<PrepaymentProductLink>> getAllLinks() async {
    return getAllLinksByEventId(1);
  }
} 