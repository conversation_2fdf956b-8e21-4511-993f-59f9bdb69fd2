import 'dart:async';
import 'validation_result.dart';
import 'validators.dart';

/// 동적 검증 규칙과 실시간 검증을 지원하는 유틸리티 클래스입니다.
/// - 조건부 검증, 체이닝 검증, 디바운싱 검증 등 고급 검증 기능
/// - 실시간 검증과 비동기 검증 지원
class ValidationRules {
  ValidationRules._();

  // ============ 실시간 검증 메서드들 ============

  /// 실시간 검증을 위한 비동기 검증 (Future 반환)
  static Future<String?> validateEmailAsync(String? email) async {
    final basicValidation = InputValidators.validateEmail(email);
    if (basicValidation != null) return basicValidation;

    // 시뮬레이션: 서버에서 이메일 중복 체크
    await Future.delayed(const Duration(milliseconds: 500));

    // 실제로는 서버 API 호출
    if (email == '<EMAIL>') {
      return '이미 사용 중인 이메일입니다.';
    }

    return null;
  }

  /// 체이닝 검증 - 여러 검증을 순서대로 실행
  static String? validateChain(
    String? value,
    List<String? Function(String?)> validators,
  ) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }

  /// 조건부 검증 - 특정 조건에서만 검증 실행
  static String? validateConditional(
    String? value,
    bool condition,
    String? Function(String?) validator,
  ) {
    if (!condition) return null;
    return validator(value);
  }

  /// 실시간 검증을 위한 디바운싱 된 검증
  static Timer? _validationTimer;

  static void validateWithDebounce(
    String? value,
    String? Function(String?) validator,
    Function(String?) onResult, {
    Duration delay = const Duration(milliseconds: 500),
  }) {
    _validationTimer?.cancel();
    _validationTimer = Timer(delay, () {
      final result = validator(value);
      onResult(result);
    });
  }

  /// 동적 검증 규칙 적용
  static ValidationResult validateDynamic(
    Map<String, dynamic> data,
    Map<String, List<ValidationRule>> rules,
  ) {
    final errors = <String, String>{};

    rules.forEach((field, fieldRules) {
      final value = data[field];

      for (final rule in fieldRules) {
        if (!rule.condition(data)) continue;

        final result = rule.validator(value);
        if (result != null) {
          errors[field] = result;
          break;
        }
      }
    });

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  // ============ 미리 정의된 검증 규칙들 ============

  /// 상품 등록 시 검증 규칙
  static Map<String, List<ValidationRule>> getProductValidationRules() {
    return {
      'name': [
        ValidationRule(
          condition: (data) => true, // 항상 검증
          validator: (value) => InputValidators.validateRequired(value, '상품명'),
          description: '상품명 필수 입력',
        ),
        ValidationRule(
          condition: (data) => data['name'] != null && data['name'].toString().isNotEmpty,
          validator: (value) => SecurityValidators.containsMaliciousInput(value.toString())
              ? '상품명에 특수문자를 포함할 수 없습니다.'
              : null,
          description: '상품명 보안 검증',
        ),
        ValidationRule(
          condition: (data) => data['name'] != null && data['name'].toString().isNotEmpty,
          validator: (value) => InputValidators.isValidProductName(value.toString())
              ? null
              : '상품명은 1-100자 사이여야 합니다.',
          description: '상품명 길이 검증',
        ),
      ],
      'price': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validatePrice(value?.toString()),
          description: '가격 필수 입력 및 형식 검증',
        ),
        ValidationRule(
          condition: (data) => data['price'] != null,
          validator: (value) => InputValidators.isValidPrice(value as int?)
              ? null
              : '가격은 0 이상이어야 합니다.',
          description: '가격 범위 검증',
        ),
      ],
      'quantity': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateStock(value?.toString()),
          description: '수량 필수 입력 및 형식 검증',
        ),
        ValidationRule(
          condition: (data) => data['quantity'] != null,
          validator: (value) => InputValidators.isValidQuantity(value as int?)
              ? null
              : '수량은 0 이상이어야 합니다.',
          description: '수량 범위 검증',
        ),
      ],
    };
  }

  /// 판매 등록 시 검증 규칙
  static Map<String, List<ValidationRule>> getSaleValidationRules() {
    return {
      'productName': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateRequired(value, '상품명'),
          description: '상품명 필수 입력',
        ),
        ValidationRule(
          condition: (data) => data['productName'] != null && data['productName'].toString().isNotEmpty,
          validator: (value) => SecurityValidators.containsMaliciousInput(value.toString())
              ? '상품명에 특수문자를 포함할 수 없습니다.'
              : null,
          description: '상품명 보안 검증',
        ),
      ],
      'quantity': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateNumber(value?.toString(), '수량'),
          description: '수량 필수 입력',
        ),
        ValidationRule(
          condition: (data) => data['quantity'] != null && data['availableStock'] != null,
          validator: (value) {
            // 재고 검증은 condition에서 이미 확인했으므로 여기서는 단순히 value만 검증
            final quantity = value as int?;
            if (quantity != null && quantity <= 0) {
              return '판매 수량은 1개 이상이어야 합니다.';
            }
            return null;
          },
          description: '수량 범위 검증',
        ),
      ],
      'buyerName': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateRequired(value, '구매자명'),
          description: '구매자명 필수 입력',
        ),
        ValidationRule(
          condition: (data) => data['buyerName'] != null && data['buyerName'].toString().isNotEmpty,
          validator: (value) => SecurityValidators.containsMaliciousInput(value.toString())
              ? '구매자명에 특수문자를 포함할 수 없습니다.'
              : null,
          description: '구매자명 보안 검증',
        ),
      ],
    };
  }

  /// 선불결제 등록 시 검증 규칙
  static Map<String, List<ValidationRule>> getPrepaymentValidationRules() {
    return {
      'buyerName': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateRequired(value, '구매자명'),
          description: '구매자명 필수 입력',
        ),
        ValidationRule(
          condition: (data) => data['buyerName'] != null && data['buyerName'].toString().isNotEmpty,
          validator: (value) => SecurityValidators.containsMaliciousInput(value.toString())
              ? '구매자명에 특수문자를 포함할 수 없습니다.'
              : null,
          description: '구매자명 보안 검증',
        ),
      ],
      'buyerContact': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateRequired(value, '연락처'),
          description: '연락처 필수 입력',
        ),
        ValidationRule(
          condition: (data) => data['buyerContact'] != null && data['buyerContact'].toString().isNotEmpty,
          validator: (value) => InputValidators.isValidContact(value.toString())
              ? null
              : '올바른 연락처를 입력해주세요 (전화번호 또는 이메일)',
          description: '연락처 형식 검증',
        ),
      ],
      'amount': [
        ValidationRule(
          condition: (data) => true,
          validator: (value) => InputValidators.validateAmount(value?.toString()),
          description: '금액 필수 입력 및 형식 검증',
        ),
        ValidationRule(
          condition: (data) => data['amount'] != null && data['productPrices'] != null,
          validator: (value) {
            // 금액 검증은 condition에서 이미 확인했으므로 여기서는 단순히 value만 검증
            final amount = value as int?;
            if (amount != null && amount <= 0) {
              return '결제 금액은 0원보다 커야 합니다.';
            }
            return null;
          },
          description: '금액 범위 검증',
        ),
      ],
    };
  }

  // ============ 복합 검증 규칙들 ============

  /// 상품 등록 시 전체 검증
  static ValidationResult validateProductWithRules(Map<String, dynamic> productData) {
    final rules = getProductValidationRules();
    return validateDynamic(productData, rules);
  }

  /// 판매 등록 시 전체 검증
  static ValidationResult validateSaleWithRules(Map<String, dynamic> saleData) {
    final rules = getSaleValidationRules();
    return validateDynamic(saleData, rules);
  }

  /// 선불결제 등록 시 전체 검증
  static ValidationResult validatePrepaymentWithRules(Map<String, dynamic> prepaymentData) {
    final rules = getPrepaymentValidationRules();
    return validateDynamic(prepaymentData, rules);
  }

  // ============ 커스텀 검증 규칙 생성 ============

  /// 조건부 필수 입력 검증 규칙 생성
  static ValidationRule createConditionalRequiredRule(
    String fieldName,
    bool Function(Map<String, dynamic> data) condition,
  ) {
    return ValidationRule(
      condition: condition,
      validator: (value) => InputValidators.validateRequired(value?.toString(), fieldName),
      description: '$fieldName 조건부 필수 입력',
    );
  }

  /// 보안 검증 규칙 생성
  static ValidationRule createSecurityValidationRule(
    String fieldName,
    bool Function(Map<String, dynamic> data) condition,
  ) {
    return ValidationRule(
      condition: condition,
      validator: (value) => SecurityValidators.containsMaliciousInput(value?.toString())
          ? '$fieldName에 특수문자를 포함할 수 없습니다.'
          : null,
      description: '$fieldName 보안 검증',
    );
  }

  /// 길이 검증 규칙 생성
  static ValidationRule createLengthValidationRule(
    String fieldName,
    int minLength,
    int maxLength,
    bool Function(Map<String, dynamic> data) condition,
  ) {
    return ValidationRule(
      condition: condition,
      validator: (value) {
        final strValue = value?.toString();
        if (strValue == null || strValue.isEmpty) return null;
        return InputValidators.isLengthInRange(strValue, minLength, maxLength)
            ? null
            : '$fieldName은 $minLength~$maxLength자 사이여야 합니다.';
      },
      description: '$fieldName 길이 검증 ($minLength-$maxLength자)',
    );
  }
} 