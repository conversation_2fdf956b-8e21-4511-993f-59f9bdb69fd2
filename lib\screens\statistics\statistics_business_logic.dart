import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/prepayment.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import 'statistics_calculator.dart';
import 'statistics_filter.dart';
import 'statistics_state.dart';

/// 통계 화면의 비즈니스 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 데이터 필터링 및 계산
/// - 상태 관리 로직
/// - 이벤트 처리 로직
/// - 비즈니스 규칙 적용
class StatisticsBusinessLogic {
  /// 필터링된 판매 로그 조회
  static List<SalesLog> getFilteredSalesLogs({
    required List<SalesLog> allSalesLogs,
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) {
    return StatisticsCalculator.getFilteredSalesLogs(
      allSalesLogs,
      selectedSeller: selectedSeller,
      selectedDateRange: selectedDateRange,
    );
  }

  /// 통계 계산
  static SalesStats calculateStats(List<SalesLog> filteredLogs, {Map<int, String>? productCategoryMap}) {
    return StatisticsCalculator.calculateStats(filteredLogs, productCategoryMap: productCategoryMap);
  }

  /// 선입금 총액 계산
  static int calculateTotalPrepaymentAmount(List<Prepayment> prepayments) {
    return StatisticsCalculator.calculateTotalPrepaymentAmount(prepayments);
  }

  /// 필터 정보 표시 여부 확인
  static bool shouldShowFilterInfo({
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) {
    return StatisticsFilter.shouldShowFilterInfo(
      selectedSeller: selectedSeller,
      selectedDateRange: selectedDateRange,
    );
  }

  /// 필터 다이얼로그 표시
  static Future<Map<String, dynamic>?> showFilterDialog({
    required BuildContext context,
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) {
    return StatisticsFilter.showFilterDialog(
      context: context,
      selectedSeller: selectedSeller,
      selectedDateRange: selectedDateRange,
    );
  }

  /// 판매자별 통계 카드 표시 여부 확인
  static bool shouldShowSellerStatsCard(String selectedSeller) {
    return selectedSeller == '전체 판매자';
  }

  /// 날짜 범위 포맷팅
  static String formatDateRange(DateTimeRange dateRange) {
    final start = dateRange.start;
    final end = dateRange.end;
    
    return '${start.year}-${start.month.toString().padLeft(2, '0')}-${start.day.toString().padLeft(2, '0')} ~ ${end.year}-${end.month.toString().padLeft(2, '0')}-${end.day.toString().padLeft(2, '0')}';
  }

  /// 통계 데이터 유효성 검사
  static bool isValidStatsData(SalesStats stats) {
    return stats.totalTransactions >= 0 && 
           stats.totalQuantity >= 0 && 
           stats.totalAmount >= 0;
  }

  /// 필터링된 데이터가 있는지 확인
  static bool hasFilteredData(List<SalesLog> filteredLogs) {
    return filteredLogs.isNotEmpty;
  }

  /// 통계 계산이 필요한지 확인
  static bool needsStatsCalculation({
    required List<SalesLog> previousLogs,
    required List<SalesLog> currentLogs,
    required String previousSeller,
    required String currentSeller,
    required DateTimeRange? previousDateRange,
    required DateTimeRange? currentDateRange,
  }) {
    // 데이터가 변경되었는지 확인
    if (previousLogs.length != currentLogs.length) return true;
    
    // 판매자 필터가 변경되었는지 확인
    if (previousSeller != currentSeller) return true;
    
    // 날짜 범위가 변경되었는지 확인
    if (previousDateRange != currentDateRange) return true;
    
    // 개별 로그 데이터가 변경되었는지 확인
    for (int i = 0; i < previousLogs.length; i++) {
      if (previousLogs[i] != currentLogs[i]) return true;
    }
    
    return false;
  }

  /// 로딩 상태 확인
  static bool isLoading({
    required AsyncValue salesLogState,
    required AsyncValue prepaymentState,
  }) {
    return salesLogState.isLoading || prepaymentState.isLoading;
  }

  /// 에러 상태 확인
  static bool hasError({
    required AsyncValue salesLogState,
    required AsyncValue prepaymentState,
  }) {
    return salesLogState.hasError || prepaymentState.hasError;
  }

  /// 에러 메시지 조합
  static String getCombinedErrorMessage({
    required AsyncValue salesLogState,
    required AsyncValue prepaymentState,
  }) {
    final errors = <String>[];
    
    if (salesLogState.hasError) {
      errors.add('판매 데이터 로딩 실패');
    }
    
    if (prepaymentState.hasError) {
      errors.add('선입금 데이터 로딩 실패');
    }
    
    return errors.join(', ');
  }

  /// 통계 카드 표시 순서 결정
  static List<String> getCardDisplayOrder({
    required bool hasFilterInfo,
    required bool hasTransactionTypeStats,
    required bool hasProductStats,
    required bool hasSellerStats,
  }) {
    final order = <String>[];
    
    if (hasFilterInfo) order.add('filter_info');
    order.add('overall_stats');
    
    if (hasTransactionTypeStats) order.add('transaction_type_stats');
    if (hasProductStats) order.add('product_stats');
    if (hasSellerStats) order.add('seller_stats');
    
    return order;
  }

  /// 통계 데이터 요약 생성
  static Map<String, dynamic> generateStatsSummary({
    required SalesStats stats,
    required int totalPrepaymentAmount,
  }) {
    final salesAmount = stats.transactionTypeStats[TransactionType.sale]?.amount ?? 0;
    final discountAmount = stats.transactionTypeStats[TransactionType.discount]?.amount ?? 0;
    final netSalesAmount = salesAmount + discountAmount;
    final totalSettlementAmount = netSalesAmount + totalPrepaymentAmount;

    return {
      'totalTransactions': stats.totalTransactions,
      'totalQuantity': stats.totalQuantity,
      'totalAmount': stats.totalAmount,
      'salesAmount': salesAmount,
      'discountAmount': discountAmount,
      'netSalesAmount': netSalesAmount,
      'totalPrepaymentAmount': totalPrepaymentAmount,
      'totalSettlementAmount': totalSettlementAmount,
      'transactionTypeCount': stats.transactionTypeStats.length,
      'productCount': stats.productStats.length,
      'sellerCount': stats.sellerStats.length,
    };
  }
} 