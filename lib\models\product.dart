/// Blue Booth Manager - 상품 데이터 모델
///
/// 상품(재고) 정보를 표현하는 데이터 모델 클래스입니다.
/// - 상품명, 수량, 가격, 이미지, 판매자, 활성/할인/포커스 등 다양한 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
///
/// 주요 특징:
/// - Freezed 기반 상태 비교 최적화
/// - 불변 객체 패턴 (immutable)
/// - JSON/SQLite 직렬화 지원
/// - copyWith 메서드로 부분 업데이트
/// - 팩토리 메서드로 다양한 생성 패턴 지원
///
/// 데이터 무결성:
/// - 필수 필드 검증
/// - 기본값 설정
/// - 타입 안전성 보장
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 6월
library;

import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'sync_metadata.dart';

part 'product.freezed.dart';
part 'product.g.dart';

/// 상품 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class Product with _$Product {
  const factory Product({
    int? id,
    required String name,
    required int price,
    required int quantity,
    String? sellerName,
    String? imagePath,
    @Default(true) bool isActive,
    int? lastServicedDate,
    @Default(1) int eventId, // 행사 ID 추가
    @Default(1) int categoryId, // 카테고리 ID 추가 (기본값: 1 - 기본 카테고리)

    // 실시간 동기화 메타데이터
    SyncMetadata? syncMetadata,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);

  factory Product.fromMap(Map<String, dynamic> map) {
    // eventId가 없거나 0인 경우 경고 로그 출력
    final eventId = map['eventId'] as int?;
    if (eventId == null || eventId <= 0) {
      // 개발 모드에서만 로그 출력
      assert(() {
        print('⚠️ Product.fromMap: eventId가 없거나 유효하지 않습니다. (eventId: $eventId, productName: ${map['name']})');
        return true;
      }());
    }

    // 동기화 메타데이터 파싱
    SyncMetadata? syncMetadata;
    if (map['syncMetadata'] != null) {
      try {
        if (map['syncMetadata'] is String) {
          syncMetadata = SyncMetadata.fromJson(
            jsonDecode(map['syncMetadata'] as String),
          );
        } else if (map['syncMetadata'] is Map<String, dynamic>) {
          syncMetadata = SyncMetadata.fromJson(
            map['syncMetadata'] as Map<String, dynamic>,
          );
        }
      } catch (e) {
        // 메타데이터 파싱 실패시 무시하고 계속 진행
        print('⚠️ Product.fromMap: syncMetadata 파싱 실패: $e');
      }
    }

    // 상품명 검증 및 정리
    final productName = (map['name'] as String?)?.trim() ?? '';
    if (productName.isEmpty) {
      throw ArgumentError('상품명은 필수 입력 항목입니다.');
    }

    return Product(
      id: map['id'] as int?,
      name: productName, // 검증된 상품명 사용
      price: map['price'] as int,
      quantity: map['quantity'] as int,
      sellerName: map['sellerName'] as String?,
      imagePath: map['imagePath'] as String?,

      isActive: (map['isActive'] ?? 1) == 1,
      lastServicedDate: map['lastServicedDate'] as int?,
      eventId: eventId ?? 1, // 기본값 1 (경고 로그와 함께)
      categoryId: map['categoryId'] as int? ?? 1, // 카테고리 ID 파싱 추가 (기본값: 1)
      syncMetadata: syncMetadata,
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  // 주의: eventId는 반드시 현재 선택된 행사 ID를 전달해야 합니다.
  factory Product.create({
    int? id,
    required String name,
    required int price,
    required int quantity,
    String? sellerName,
    String? imagePath,
    bool isActive = true,
    int? lastServicedDate,
    required int eventId, // 행사 ID 필수로 변경
    int categoryId = 1, // 카테고리 ID 추가 (기본값: 1)
  }) {
    return Product(
      id: id,
      name: name,
      price: price,
      quantity: quantity,
      sellerName: sellerName,
      imagePath: imagePath,

      isActive: isActive,
      lastServicedDate: lastServicedDate,
      eventId: eventId,
      categoryId: categoryId, // 카테고리 ID 전달
    );
  }
}



// 재고 상태 관련 Extension
extension ProductStock on Product {
  bool get isOutOfStock => quantity <= 0;
  bool get isLowStock => quantity <= 5 && quantity > 0;
}

// SQLite 맵 변환을 위한 Extension
extension ProductMapper on Product {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'name': name,
      'price': price,
      'quantity': quantity,
      'sellerName': sellerName,
      'imagePath': imagePath,

      'isActive': isActive ? 1 : 0,
      'lastServicedDate': lastServicedDate,
      'eventId': eventId,
      'categoryId': categoryId, // 카테고리 ID 저장 추가
    };
    
    // 동기화 메타데이터 추가
    if (syncMetadata != null) {
      map['syncMetadata'] = jsonEncode(syncMetadata!.toJson());
    }
    
    // id가 null이 아닌 경우에만 추가 (AUTOINCREMENT를 위해)
    if (id != null) {
      map['id'] = id;
    }
    
    return map;
  }

  /// 실시간 동기화를 위한 업데이트된 상품 생성
  Product withSyncUpdate({
    String? deviceId,
    SyncStatus? syncStatus,
  }) {
    final updatedMetadata = syncMetadata != null
        ? SyncMetadata.updated(
            syncMetadata!,
            deviceId: deviceId,
            newStatus: syncStatus,
          )
        : SyncMetadata.create(deviceId: deviceId);

    return copyWith(syncMetadata: updatedMetadata);
  }

  /// 새 상품 생성시 동기화 메타데이터 포함
  Product withSyncMetadata({String? deviceId}) {
    return copyWith(
      syncMetadata: SyncMetadata.create(deviceId: deviceId),
    );
  }
}
