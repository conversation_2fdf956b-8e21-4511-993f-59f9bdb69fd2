/**
 * Firebase Functions for User Data Management (2nd Generation)
 *
 * 사용자 계정 삭제 시 모든 관련 데이터를 안전하게 정리하는 Functions
 */

const { onCall } = require('firebase-functions/v2/https');
const admin = require("firebase-admin");

// Firebase Admin SDK 초기화 (이미 초기화되어 있으면 중복 방지)
if (!admin.apps.length) {
  const serviceAccount = require("./parabara-1a504-288e9c6e5d05.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "parabara-1a504.firebasestorage.app"
  });
}

/**
 * 사용자 데이터 완전 삭제 함수 (2세대 onCall)
 * 클라이언트에서 호출하여 모든 데이터를 안전하게 삭제합니다.
 */
exports.deleteUserData = onCall(async (request) => {
  // 인증 확인
  if (!request.auth) {
    throw new Error('인증이 필요합니다.');
  }
  
  const uid = request.auth.uid;
  const userEmail = request.auth.token.email || 'unknown';
  
  console.log(`🗑️ 사용자 데이터 삭제 시작: ${userEmail} (${uid})`);
  
  try {
    // 1. Firestore 데이터 재귀 삭제 (사용자 문서 + 모든 하위 컬렉션)
    console.log(`📄 Firestore 재귀 삭제 시작: ${uid}`);
    const userRef = admin.firestore().collection('users').doc(uid);
    await admin.firestore().recursiveDelete(userRef);
    console.log(`✅ Firestore 모든 데이터 삭제 완료: ${uid}`);
    
    // 2. Storage 파일 전체 삭제 (사용자 폴더 통째로)
    console.log(`🗂️ Storage 폴더 삭제 시작: users/${uid}/`);
    const bucket = admin.storage().bucket();
    const [files] = await bucket.getFiles({ prefix: `users/${uid}/` });
    
    if (files.length > 0) {
      await Promise.all(files.map(file => file.delete()));
      console.log(`✅ Storage 파일 삭제 완료: ${files.length}개 파일`);
    } else {
      console.log(`📁 Storage에 삭제할 파일 없음`);
    }
    
    // 3. Authentication 계정 삭제 (마지막에)
    console.log(`🔐 Auth 계정 삭제 시작: ${uid}`);
    await admin.auth().deleteUser(uid);
    console.log(`✅ Auth 계정 삭제 완료: ${uid}`);
    
    console.log(`🎉 사용자 데이터 완전 삭제 완료: ${userEmail} (${uid})`);
    
    return { 
      success: true, 
      message: '모든 사용자 데이터가 성공적으로 삭제되었습니다.',
      deletedUid: uid 
    };
    
  } catch (error) {
    console.error(`❌ 사용자 데이터 삭제 실패: ${userEmail} (${uid})`, error);
    throw new Error(`데이터 삭제 중 오류가 발생했습니다: ${error.message}`);
  }
});

// 기존의 복잡한 함수들은 더 이상 필요 없음!
