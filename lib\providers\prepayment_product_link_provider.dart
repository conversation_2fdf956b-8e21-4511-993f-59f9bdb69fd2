import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prepayment_product_link.dart';
import '../repositories/prepayment_product_link_repository.dart';
import '../services/database_service.dart';
import '../services/realtime_sync_service_main.dart';
import '../providers/realtime_sync_provider.dart';
import '../models/event_workspace.dart';
import '../utils/logger_utils.dart';
import 'unified_workspace_provider.dart';

final prepaymentProductLinkRepositoryProvider = Provider<PrepaymentProductLinkRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return PrepaymentProductLinkRepository(database: databaseService);
});

class PrepaymentProductLinkNotifier extends StateNotifier<List<PrepaymentProductLink>> {
  final PrepaymentProductLinkRepository repository;
  final Ref ref;
  String? errorMessage;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;

  // 무한 루프 방지를 위한 최근 추가한 연동 캐시
  final Set<String> _recentlyAddedLinks = <String>{};

  PrepaymentProductLinkNotifier(this.repository, this.ref) : super([]) {
    _setupRealtimeSync();
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();

      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: 'PrepaymentProductLinkNotifier');
        return;
      }

      // 실시간 동기화 서비스 가져오기
      final realtimeService = ref.read(realtimeSyncServiceProvider);

      LoggerUtils.logInfo('선입금-상품 연동 실시간 데이터 스트림 구독 시작', tag: 'PrepaymentProductLinkNotifier');

      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        _handleRealtimeDataChange(change);
      });
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 설정 실패', tag: 'PrepaymentProductLinkNotifier', error: e);
    }
  }

  /// 실시간 데이터 변경 처리
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);

      // 현재 워크스페이스의 선입금-상품 연동 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'prepayment_product_links') {
        // 자기가 최근에 추가한 연동은 무시 (무한 루프 방지)
        if (_recentlyAddedLinks.contains(change.documentId)) {
          LoggerUtils.logDebug('최근 추가한 연동 무시: ID ${change.documentId}', tag: 'PrepaymentProductLinkNotifier');
          return;
        }

        LoggerUtils.logInfo('선입금-상품 연동 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: 'PrepaymentProductLinkNotifier');

        // 연동 목록 즉시 새로고침 (로딩 없이)
        loadLinks(showLoading: false);
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: 'PrepaymentProductLinkNotifier', error: e);
    }
  }

  @override
  void dispose() {
    _realtimeSubscription?.cancel();
    super.dispose();
  }

  Future<void> loadLinks({bool showLoading = true}) async {
    // 현재 행사 워크스페이스 확인
    EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

    if (currentWorkspace == null) {
      state = [];
      errorMessage = '워크스페이스를 선택해주세요';
      return;
    }

    int retry = 0;
    while (retry < 3) {
      try {
        final links = await repository.getAllLinksByEventId(currentWorkspace.id);
        state = links;
        errorMessage = null;
        return;
      } catch (e) {
        final msg = e.toString();
        if (msg.contains('no such table') || msg.contains('database is not open')) {
          retry++;
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        } else {
          errorMessage = msg;
          return;
        }
      }
    }
    errorMessage = '데이터베이스 초기화 중입니다. 잠시 후 다시 시도해주세요.';
  }

  Future<void> addLink(PrepaymentProductLink link) async {
    await repository.insertLink(link);

    // 최근 추가한 연동으로 캐시 (무한 루프 방지용)
    final linkId = '${link.virtualProductId}_${link.productId}';
    _recentlyAddedLinks.add(linkId);
    // 5초 후 캐시에서 제거
    Future.delayed(const Duration(seconds: 5), () {
      _recentlyAddedLinks.remove(linkId);
    });

    // Firebase에 추가
    try {
      final realtimeService = ref.read(realtimeSyncServiceProvider);
      await realtimeService.addPrepaymentProductLink(link.eventId, link);
      LoggerUtils.logInfo('선입금-상품 연동 Firebase 추가 성공: ${link.virtualProductId} -> ${link.productId}', tag: 'PrepaymentProductLinkNotifier');
    } catch (e) {
      LoggerUtils.logError('선입금-상품 연동 Firebase 추가 실패: ${link.virtualProductId} -> ${link.productId}', tag: 'PrepaymentProductLinkNotifier', error: e);
    }

    await loadLinks(showLoading: false);
  }

  Future<void> removeLink(int virtualProductId, int productId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    await repository.deleteLink(virtualProductId, productId, currentWorkspace.id);

    // Firebase에서 삭제
    try {
      final realtimeService = ref.read(realtimeSyncServiceProvider);
      await realtimeService.deletePrepaymentProductLink(currentWorkspace.id, virtualProductId, productId);
      LoggerUtils.logInfo('선입금-상품 연동 Firebase 삭제 성공: $virtualProductId -> $productId', tag: 'PrepaymentProductLinkNotifier');
    } catch (e) {
      LoggerUtils.logError('선입금-상품 연동 Firebase 삭제 실패: $virtualProductId -> $productId', tag: 'PrepaymentProductLinkNotifier', error: e);
    }

    await loadLinks(showLoading: false);
  }

  Future<void> loadLinksByVirtualProduct(int virtualProductId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      state = [];
      return;
    }

    final links = await repository.getLinksByVirtualProductId(virtualProductId, currentWorkspace.id);
    state = links;
  }

  Future<void> loadLinksByProduct(int productId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      state = [];
      return;
    }

    final links = await repository.getLinksByProductId(productId, currentWorkspace.id);
    state = links;
  }
}

final prepaymentProductLinkNotifierProvider = StateNotifierProvider<PrepaymentProductLinkNotifier, List<PrepaymentProductLink>>((ref) {
  final repository = ref.watch(prepaymentProductLinkRepositoryProvider);
  return PrepaymentProductLinkNotifier(repository, ref);
});