# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

analyzer:
  plugins:
    - custom_lint
  exclude:
    - test/**
  errors:
    # 중요한 경고들은 유지하고, 정말 필요한 것만 무시
    # 에러 핸들링 유틸리티와 상태 캐싱에서 필요한 패턴이므로 무시
    invalid_use_of_visible_for_testing_member: ignore
    invalid_use_of_protected_member: ignore
    # 나머지는 경고로 표시하여 개발자가 인지할 수 있도록 함
    unused_import: warning
    unused_local_variable: warning
    unused_field: warning
    unnecessary_cast: warning
    override_on_non_overriding_member: warning
    dead_null_aware_expression: warning
    unnecessary_non_null_assertion: warning
    invalid_null_aware_operator: warning
    unused_element: warning

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options