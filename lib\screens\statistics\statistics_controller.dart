import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/sales_log.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/sales_log_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import 'statistics_business_logic.dart';

/// 통계 화면의 상태 관리 및 이벤트 처리를 담당하는 클래스
///
/// 주요 기능:
/// - 상태 관리 (선택된 판매자, 날짜 범위 등)
/// - 이벤트 처리 (필터 변경, 데이터 새로고침 등)
/// - UI 상태 업데이트
/// - 데이터 흐름 제어
class StatisticsController {
  final WidgetRef ref;
  final BuildContext context;

  StatisticsController({
    required this.ref,
    required this.context,
  });

  /// 현재 선택된 판매자
  String _selectedSeller = '전체 판매자';

  /// 현재 선택된 날짜 범위
  DateTimeRange? _selectedDateRange;

  /// 이전 판매 로그 데이터 (변경 감지용)
  List<SalesLog>? _previousSalesLogs;

  /// 이전 선입금 데이터 (변경 감지용)
  List<dynamic>? _previousPrepayments;

  /// 현재 선택된 판매자 반환
  String get selectedSeller => _selectedSeller;

  /// 현재 선택된 날짜 범위 반환
  DateTimeRange? get selectedDateRange => _selectedDateRange;

  /// 판매자 필터 변경
  void updateSelectedSeller(String seller) {
    _selectedSeller = seller;
    _notifyStateChange();
  }

  /// 날짜 범위 필터 변경
  void updateSelectedDateRange(DateTimeRange? dateRange) {
    _selectedDateRange = dateRange;
    _notifyStateChange();
  }

  /// 필터 초기화
  void resetFilters() {
    _selectedSeller = '전체 판매자';
    _selectedDateRange = null;
    _notifyStateChange();
  }

  /// 필터 다이얼로그 표시
  Future<void> showFilterDialog() async {
    final result = await StatisticsBusinessLogic.showFilterDialog(
      context: context,
      selectedSeller: _selectedSeller,
      selectedDateRange: _selectedDateRange,
    );

    if (result != null) {
      _selectedSeller = result['selectedSeller'] as String;
      _selectedDateRange = result['selectedDateRange'] as DateTimeRange?;
      _notifyStateChange();
    }
  }

  /// 필터링된 판매 로그 조회
  List<SalesLog> getFilteredSalesLogs() {
    final allSalesLogs = ref.watch(salesLogsProvider);
    
    return StatisticsBusinessLogic.getFilteredSalesLogs(
      allSalesLogs: allSalesLogs,
      selectedSeller: _selectedSeller,
      selectedDateRange: _selectedDateRange,
    );
  }

  /// 통계 계산
  Map<String, dynamic> calculateStatistics() {
    final filteredLogs = getFilteredSalesLogs();
    final prepaymentState = ref.watch(prepaymentNotifierProvider);
    final prepayments = prepaymentState.prepayments;

    // productCategoryMap 생성
    Map<int, String>? productCategoryMap;
    final productsAsync = ref.read(productNotifierProvider);
    final categoriesAsync = ref.read(categoryNotifierProvider);

    if (!productsAsync.isLoading && !productsAsync.hasError &&
        categoriesAsync.hasValue) {
      final products = productsAsync.products;
      final categories = categoriesAsync.value!;

      // productId -> categoryName 매핑 생성
      productCategoryMap = <int, String>{};
      for (final product in products) {
        try {
          final category = categories.firstWhere(
            (cat) => cat.id == product.categoryId,
          );
          if (product.id != null) {
            productCategoryMap[product.id!] = category.name;
          }
        } catch (e) {
          // 카테고리를 찾을 수 없는 경우 로그만 출력하고 계속 진행
        }
      }
    }

    final stats = StatisticsBusinessLogic.calculateStats(filteredLogs, productCategoryMap: productCategoryMap);
    final totalPrepaymentAmount = StatisticsBusinessLogic.calculateTotalPrepaymentAmount(prepayments);

    return {
      'stats': stats,
      'totalPrepaymentAmount': totalPrepaymentAmount,
      'filteredLogs': filteredLogs,
      'prepayments': prepayments,
    };
  }

  /// 로딩 상태 확인
  bool isLoading() {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);
    
    // 임시로 직접 상태 확인
    return salesLogState.isLoading || prepaymentState.isLoading;
  }

  /// 에러 상태 확인
  bool hasError() {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);
    
    // 임시로 직접 상태 확인  
    return salesLogState.hasError || prepaymentState.hasError;
  }

  /// 에러 메시지 조합
  String getErrorMessage() {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);
    
    // 임시로 직접 상태 확인
    final errors = <String>[];
    if (salesLogState.hasError && salesLogState.errorMessage != null) {
      errors.add(salesLogState.errorMessage!);
    }
    if (prepaymentState.hasError && prepaymentState.errorMessage != null) {
      errors.add(prepaymentState.errorMessage!);
    }
    return errors.join(', ');
  }

  /// 필터 정보 표시 여부 확인
  bool shouldShowFilterInfo() {
    return StatisticsBusinessLogic.shouldShowFilterInfo(
      selectedSeller: _selectedSeller,
      selectedDateRange: _selectedDateRange,
    );
  }

  /// 판매자별 통계 카드 표시 여부 확인
  bool shouldShowSellerStatsCard() {
    return StatisticsBusinessLogic.shouldShowSellerStatsCard(_selectedSeller);
  }

  /// 통계 계산이 필요한지 확인
  bool needsStatsCalculation() {
    final currentLogs = getFilteredSalesLogs();
    final prepaymentState = ref.watch(prepaymentNotifierProvider);
    final currentPrepayments = prepaymentState.prepayments;
    
    if (_previousSalesLogs == null || _previousPrepayments == null) {
      _previousSalesLogs = currentLogs;
      _previousPrepayments = currentPrepayments;
      return true;
    }
    
    final needsCalculation = StatisticsBusinessLogic.needsStatsCalculation(
      previousLogs: _previousSalesLogs!,
      currentLogs: currentLogs,
      previousSeller: _selectedSeller,
      currentSeller: _selectedSeller,
      previousDateRange: _selectedDateRange,
      currentDateRange: _selectedDateRange,
    );
    
    if (needsCalculation) {
      _previousSalesLogs = currentLogs;
      _previousPrepayments = currentPrepayments;
    }
    
    return needsCalculation;
  }

  /// 통계 데이터 요약 생성
  Map<String, dynamic> generateStatsSummary() {
    final calculationResult = calculateStatistics();
    final stats = calculationResult['stats'];
    final totalPrepaymentAmount = calculationResult['totalPrepaymentAmount'];
    
    return StatisticsBusinessLogic.generateStatsSummary(
      stats: stats,
      totalPrepaymentAmount: totalPrepaymentAmount,
    );
  }

  /// 통계 카드 표시 순서 결정
  List<String> getCardDisplayOrder() {
    final hasFilterInfo = shouldShowFilterInfo();
    final hasTransactionTypeStats = true; // 항상 표시
    final hasProductStats = true; // 항상 표시
    final hasSellerStats = shouldShowSellerStatsCard();
    
    return StatisticsBusinessLogic.getCardDisplayOrder(
      hasFilterInfo: hasFilterInfo,
      hasTransactionTypeStats: hasTransactionTypeStats,
      hasProductStats: hasProductStats,
      hasSellerStats: hasSellerStats,
    );
  }

  /// 데이터 새로고침
  Future<void> refreshData() async {
    try {
      // invalidate를 사용한 즉시 상태 갱신
      ref.invalidate(salesLogNotifierProvider);
      ref.invalidate(prepaymentNotifierProvider);
    } catch (e) {
      // 에러 처리는 UI에서 담당
      rethrow;
    }
  }

  /// 상태 변경 알림 (StatefulWidget의 setState 호출)
  void _notifyStateChange() {
    // StatefulWidget의 setState를 호출하기 위해 콜백 사용
    if (context.mounted) {
      // 상태 변경을 알리는 방법 (실제로는 StatefulWidget에서 처리)
    }
  }

  /// 메모리 정리
  void dispose() {
    _previousSalesLogs = null;
    _previousPrepayments = null;
  }

  /// 현재 필터 상태를 문자열로 반환
  String getFilterStatusText() {
    final parts = <String>[];
    
    if (_selectedSeller != '전체 판매자') {
      parts.add('판매자: $_selectedSeller');
    }
    
    if (_selectedDateRange != null) {
      parts.add('기간: ${StatisticsBusinessLogic.formatDateRange(_selectedDateRange!)}');
    }
    
    if (parts.isEmpty) {
      return '필터 없음';
    }
    
    return parts.join(', ');
  }

  /// 필터가 적용되었는지 확인
  bool hasActiveFilters() {
    return _selectedSeller != '전체 판매자' || _selectedDateRange != null;
  }

  /// 필터링된 데이터가 있는지 확인
  bool hasFilteredData() {
    final filteredLogs = getFilteredSalesLogs();
    return StatisticsBusinessLogic.hasFilteredData(filteredLogs);
  }

  /// 통계 데이터 유효성 검사
  bool isValidStatsData() {
    final calculationResult = calculateStatistics();
    final stats = calculationResult['stats'];
    
    return StatisticsBusinessLogic.isValidStatsData(stats);
  }
} 