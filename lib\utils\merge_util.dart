/// 간단한 충돌 병합 유틸 (초기 버전)
/// - 기본 전략: 최신 수정 시간 우선(LWW)
/// - 데이터 유형별 특수 규칙 최소 적용
class MergeUtil {
  // timestamp 필드 키 후보
  static const updatedAtKeys = ['updatedAt', 'lastModified'];

  /// 두 JSON(맵) 데이터를 병합합니다.
  /// - 기본: updatedAt 비교 후 최신 쪽(LWW) 채택
  /// - Tombstone(삭제 표시) 존재 시 삭제 우선
  /// - 향후 필드별 병합 훅으로 확장 가능
  static Map<String, dynamic> mergeMaps(
    Map<String, dynamic> local,
    Map<String, dynamic> remote,
  ) {
    // 1) Tombstone 우선 처리
    if (_isTombstone(local)) return Map<String, dynamic>.from(local);
    if (_isTombstone(remote)) return Map<String, dynamic>.from(remote);

    // 2) LWW 기본 병합
    final localTs = _extractTimestamp(local);
    final remoteTs = _extractTimestamp(remote);

    final base = (remoteTs != null && (localTs == null || remoteTs.isAfter(localTs)))
        ? Map<String, dynamic>.from(remote)
        : Map<String, dynamic>.from(local);

    // 3) 필드별 병합(안전 규칙만 활성):
    // - 문자열: 비어있으면 다른 쪽의 비어있지 않은 값을 채움
    // - 리스트: set-merge(중복 제거하여 합치기)
    // - 불변 필드 보호(id, eventId): 이미 존재하면 유지
    final other = identical(base, remote) ? local : remote;
    _fillEmptyStrings(base, other);
    _mergeLists(base, other);
    _protectImmutable(base, other, keys: const ['id', 'eventId']);

    return base;
  }

  /// 모델 병합: 타입에 따라 향후 세분화 가능
  static T mergeModel<T>(T local, T remote) {
    // 현재는 단순히 remote 우선(사실상 서버 LWW)으로 둔다.
    return remote;
  }

  static bool _isTombstone(Map<String, dynamic> map) {
    // 흔한 tombstone 시그널: deleted/isDeleted=true 혹은 '_deleted': true
    final v = map['deleted'] ?? map['isDeleted'] ?? map['_deleted'];
    return v == true;
  }

  // 외부에서도 tombstone 여부 확인 가능하도록 공개 메서드 제공
  static bool isTombstone(Map<String, dynamic> map) => _isTombstone(map);

  // 타임스탬프 비교(-1: local 최신, 0: 동일/부재, 1: remote 최신)
  static int compareByTimestamp(Map<String, dynamic> local, Map<String, dynamic> remote) {
    final lt = _extractTimestamp(local);
    final rt = _extractTimestamp(remote);
    if (lt == null && rt == null) return 0;
    if (lt == null && rt != null) return 1;
    if (lt != null && rt == null) return -1;
    if (rt!.isAfter(lt!)) return 1;
    if (lt.isAfter(rt)) return -1;
    return 0;
  }

  static DateTime? _extractTimestamp(Map<String, dynamic> map) {
    // 0) syncMetadata.lastModified 우선 시도
    final sync = map['syncMetadata'];
    if (sync is Map<String, dynamic>) {
      final lm = sync['lastModified'];
      final ts = _parseTimestamp(lm);
      if (ts != null) return ts;
    }

    // 1) 일반 키 후보에서 탐색
    for (final key in updatedAtKeys) {
      final ts = _parseTimestamp(map[key]);
      if (ts != null) return ts;
    }
    return null;
  }

  static DateTime? _parseTimestamp(dynamic v) {
    if (v is DateTime) return v;
    if (v is String) {
      try { return DateTime.parse(v); } catch (_) {}
    }
    if (v is int) {
      return DateTime.fromMillisecondsSinceEpoch(v, isUtc: true);
    }
    return null;
  }

  static void _fillEmptyStrings(Map<String, dynamic> base, Map<String, dynamic> other) {
    other.forEach((key, value) {
      if (value is String && value.isNotEmpty) {
        final current = base[key];
        if (current is String && current.isEmpty) {
          base[key] = value;
        }
      }
    });
  }

  static void _mergeLists(Map<String, dynamic> base, Map<String, dynamic> other) {
    other.forEach((key, value) {
      final a = base[key];
      if (a is List && value is List) {
        final set = {...a, ...value};
        base[key] = set.toList();
      }
    });
  }

  static void _protectImmutable(Map<String, dynamic> base, Map<String, dynamic> other, {required List<String> keys}) {
    for (final key in keys) {
      if (other.containsKey(key) && base.containsKey(key)) {
        // base가 이미 값이 있으면 유지
        // 다른 쪽의 값은 무시
      }
    }
  }
}

