import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';

import '../models/revenue_goal.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 목표 수익 Repository Provider
final revenueGoalRepositoryProvider = Provider<RevenueGoalRepository>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  return RevenueGoalRepository(databaseService);
});

/// 목표 수익 Repository
class RevenueGoalRepository {
  static const String _tag = 'RevenueGoalRepository';
  static const String _tableName = 'revenue_goals';
  
  final DatabaseService _databaseService;

  RevenueGoalRepository(this._databaseService);

  /// 테이블 생성 SQL
  static String get createTableSql => '''
    CREATE TABLE IF NOT EXISTS $_tableName (
      id TEXT PRIMARY KEY,
      event_id INTEGER NOT NULL,
      seller_id TEXT,
      date TEXT NOT NULL,
      target_amount REAL NOT NULL,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      UNIQUE(event_id, seller_id, date)
    )
  ''';

  /// 행사별 목표 수익 목록 조회
  Future<List<RevenueGoal>> getGoalsByEventId(int eventId) async {
    try {
      LoggerUtils.logInfo('행사별 목표 수익 목록 조회 시작: eventId $eventId', tag: _tag);
      
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: 'event_id = ?',
        whereArgs: [eventId],
        orderBy: 'date DESC, seller_id ASC',
      );

      final goals = maps.map((map) => _mapToRevenueGoal(map)).toList();
      LoggerUtils.logInfo('행사별 목표 수익 목록 조회 완료: ${goals.length}개', tag: _tag);
      
      return goals;
    } catch (e) {
      LoggerUtils.logError('행사별 목표 수익 목록 조회 실패: eventId $eventId', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 특정 날짜와 판매자의 목표 수익 조회
  Future<RevenueGoal?> getGoal(int eventId, String? sellerId, String date) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        where: 'event_id = ? AND seller_id ${sellerId == null ? 'IS NULL' : '= ?'} AND date = ?',
        whereArgs: sellerId == null ? [eventId, date] : [eventId, sellerId, date],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return _mapToRevenueGoal(maps.first);
    } catch (e) {
      LoggerUtils.logError('목표 수익 조회 실패: eventId $eventId, sellerId $sellerId, date $date', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 목표 수익 추가
  Future<String> addGoal(RevenueGoal goal) async {
    try {
      LoggerUtils.logInfo('목표 수익 추가 시작', tag: _tag);
      
      final db = await _databaseService.database;
      final id = goal.id ?? DateTime.now().millisecondsSinceEpoch.toString();
      final goalWithId = goal.copyWith(id: id);
      
      await db.insert(
        _tableName,
        _revenueGoalToMap(goalWithId),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      LoggerUtils.logInfo('목표 수익 추가 완료: ID $id', tag: _tag);
      return id;
    } catch (e) {
      LoggerUtils.logError('목표 수익 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 목표 수익 수정
  Future<void> updateGoal(RevenueGoal goal) async {
    try {
      LoggerUtils.logInfo('목표 수익 수정 시작: ID ${goal.id}', tag: _tag);
      
      final db = await _databaseService.database;
      final updatedGoal = goal.copyWith(updatedAt: DateTime.now());
      
      await db.update(
        _tableName,
        _revenueGoalToMap(updatedGoal),
        where: 'id = ?',
        whereArgs: [goal.id],
      );

      LoggerUtils.logInfo('목표 수익 수정 완료: ID ${goal.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('목표 수익 수정 실패: ID ${goal.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 목표 수익 삭제
  Future<void> deleteGoal(String id) async {
    try {
      LoggerUtils.logInfo('목표 수익 삭제 시작: ID $id', tag: _tag);
      
      final db = await _databaseService.database;
      await db.delete(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      );

      LoggerUtils.logInfo('목표 수익 삭제 완료: ID $id', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('목표 수익 삭제 실패: ID $id', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 행사의 모든 목표 수익 삭제
  Future<void> deleteGoalsByEventId(int eventId) async {
    try {
      LoggerUtils.logInfo('행사별 목표 수익 전체 삭제 시작: eventId $eventId', tag: _tag);
      
      final db = await _databaseService.database;
      await db.delete(
        _tableName,
        where: 'event_id = ?',
        whereArgs: [eventId],
      );

      LoggerUtils.logInfo('행사별 목표 수익 전체 삭제 완료: eventId $eventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사별 목표 수익 전체 삭제 실패: eventId $eventId', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Map을 RevenueGoal로 변환
  RevenueGoal _mapToRevenueGoal(Map<String, dynamic> map) {
    return RevenueGoal(
      id: map['id'] as String,
      eventId: map['event_id'] as int,
      sellerId: map['seller_id'] as String?,
      date: map['date'] as String,
      targetAmount: (map['target_amount'] as num).toDouble(),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// RevenueGoal을 Map으로 변환
  Map<String, dynamic> _revenueGoalToMap(RevenueGoal goal) {
    return {
      'id': goal.id,
      'event_id': goal.eventId,
      'seller_id': goal.sellerId,
      'date': goal.date,
      'target_amount': goal.targetAmount,
      'created_at': goal.createdAt.toIso8601String(),
      'updated_at': goal.updatedAt.toIso8601String(),
    };
  }
}
