import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../utils/image_cache.dart';
import '../utils/logger_utils.dart';
import '../utils/mobile_performance_utils.dart';
import 'package:intl/date_symbol_data_local.dart';

/// 공통 초기화 (플러터 바인딩, 시스템 UI, Firebase 등)
Future<void> bootstrapApplication() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.light,
    statusBarIconBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.white,
    systemNavigationBarDividerColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  ImageCacheManager.initialize();
  ImageCacheManager.schedulePeriodicOptimization();

  try {
    await Firebase.initializeApp();
    LoggerUtils.logInfo('Firebase 초기화 완료 (bootstrap)', tag: 'Bootstrap');
    try {
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);
    } catch (e) {
      LoggerUtils.logError('Firebase Analytics 비활성화 실패', tag: 'Bootstrap', error: e);
    }
  } catch (e) {
    LoggerUtils.logError('Firebase 초기화 실패', tag: 'Bootstrap', error: e);
  }

  await initializeDateFormatting('ko_KR', null);
  // 성능 최적화 초기화 (레거시 main.dart 흐름 복원)
  MobilePerformanceUtils.initializeOptimizations();
}
