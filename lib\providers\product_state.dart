import '../models/product.dart';
import '../models/product_sort_option.dart';
import 'base_state.dart';

/// 상품 관리 상태 클래스
class ProductState extends BaseState {
  final List<Product> products;
  final List<Product> filteredProducts;
  final List<String> sellerNames;
  final String selectedSellerFilter;
  final ProductSortOption currentSortOption;
  final String searchQuery;
  final int totalItems;

  const ProductState({
    this.products = const [],
    this.filteredProducts = const [],
    this.sellerNames = const [],
    this.selectedSellerFilter = '',
    this.currentSortOption = ProductSortOption.recentlyAdded,
    this.searchQuery = '',
    this.totalItems = 0,
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  /// 초기 상태 반환
  factory ProductState.initial() {
    return const ProductState();
  }

  @override
  ProductState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  ProductState copyWith({
    List<Product>? products,
    List<Product>? filteredProducts,
    List<String>? sellerNames,
    String? selectedSellerFilter,
    String? searchQuery,
    int? totalItems,
    ProductSortOption? currentSortOption,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return ProductState(
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      sellerNames: sellerNames ?? this.sellerNames,
      selectedSellerFilter: selectedSellerFilter ?? this.selectedSellerFilter,
      searchQuery: searchQuery ?? this.searchQuery,
      totalItems: totalItems ?? this.totalItems,
      currentSortOption: currentSortOption ?? this.currentSortOption,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  /// 상태 변경 감지를 위한 최적화된 메서드들
  
  /// 상품 목록이 변경되었는지 확인 (참조 동일성 + 내용 비교)
  bool hasProductsChanged(List<Product> newProducts) {
    if (products.length != newProducts.length) return true;
    for (int i = 0; i < products.length; i++) {
      if (products[i] != newProducts[i]) return true;
    }
    return false;
  }

  /// 필터된 상품 목록이 변경되었는지 확인
  bool hasFilteredProductsChanged(List<Product> newFilteredProducts) {
    if (filteredProducts.length != newFilteredProducts.length) return true;
    for (int i = 0; i < filteredProducts.length; i++) {
      if (filteredProducts[i] != newFilteredProducts[i]) return true;
    }
    return false;
  }

  /// 판매자 목록이 변경되었는지 확인
  bool hasSellerNamesChanged(List<String> newSellerNames) {
    if (sellerNames.length != newSellerNames.length) return true;
    for (int i = 0; i < sellerNames.length; i++) {
      if (sellerNames[i] != newSellerNames[i]) return true;
    }
    return false;
  }

  /// 로딩 상태가 변경되었는지 확인
  bool hasLoadingChanged(bool newLoading) {
    return isLoading != newLoading;
  }

  /// 에러 상태가 변경되었는지 확인
  bool hasErrorChanged(String? newError) {
    return errorMessage != newError;
  }

  /// 필터가 변경되었는지 확인
  bool hasFilterChanged(String newFilter) {
    return selectedSellerFilter != newFilter;
  }

  /// 정렬 옵션이 변경되었는지 확인
  bool hasSortOptionChanged(ProductSortOption newSortOption) {
    return currentSortOption != newSortOption;
  }

  /// 전체 상태가 변경되었는지 확인 (성능 최적화용)
  bool hasStateChanged(ProductState newState) {
    return this != newState;
  }

  @override
  List<Object?> get props => [
    ...super.props,
    products,
    filteredProducts,
    sellerNames,
    selectedSellerFilter,
    currentSortOption,
  ];

  /// 초기 상태 반환
  static ProductState initialState() {
    return const ProductState();
  }
} 