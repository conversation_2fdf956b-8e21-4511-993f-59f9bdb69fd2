import 'package:sqflite/sqflite.dart';
import '../models/sales_stat_item.dart';
import '../models/transaction_type.dart';
import '../services/database_service.dart';
import '../utils/sql_utils.dart';

/// SalesLog의 통계 관련 작업을 관리하는 클래스
class SalesLogStats {
  static const String _tableName = DatabaseServiceImpl.salesLogTable;

  // 허용된 테이블 목록
  static const List<String> _allowedTables = ['sales_log'];

  final DatabaseService _databaseService;

  SalesLogStats({required DatabaseService database})
    : _databaseService = database;

  Future<Database> get _database => _databaseService.database;

  // 전체 판매 통계
  Future<List<SalesStatItem>> getOverallSalesStats() async {
    if (!SqlUtils.isValidTableName(_tableName, _allowedTables)) {
      throw ArgumentError('Invalid table name');
    }

    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        sl.sellerName,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalDiscountAmount,
        (SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) - 
         SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END)) as netSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN 1 ELSE 0 END) as salesCount
      FROM $_tableName sl
      WHERE sl.sellerName IS NOT NULL
      GROUP BY sl.sellerName
      ORDER BY sl.sellerName ASC
    ''',
      [
        TransactionType.sale.value,
        TransactionType.discount.value,
        TransactionType.sale.value,
        TransactionType.discount.value,
        TransactionType.sale.value,
      ],
    );

    return maps.map((map) => SalesStatItem.fromMap(map)).toList();
  }

  // 특정 판매자 판매 통계
  Future<List<SalesStatItem>> getSalesStatsBySeller(String sellerName) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        sl.sellerName,
        SUM(CASE WHEN sl.transactionType = 'SALE' THEN sl.totalAmount ELSE 0 END) as totalSalesAmount,
        SUM(CASE WHEN sl.transactionType = 'DISCOUNT' THEN sl.totalAmount ELSE 0 END) as totalDiscountAmount,
        (SUM(CASE WHEN sl.transactionType = 'SALE' THEN sl.totalAmount ELSE 0 END) - 
         SUM(CASE WHEN sl.transactionType = 'DISCOUNT' THEN sl.totalAmount ELSE 0 END)) as netSalesAmount,
        SUM(CASE WHEN sl.transactionType = 'SALE' THEN 1 ELSE 0 END) as salesCount
      FROM ${DatabaseServiceImpl.salesLogTable} sl
      WHERE sl.sellerName = ?
      GROUP BY sl.sellerName
      ORDER BY sl.sellerName ASC
    ''',
      [sellerName],
    );

    return maps.map((map) => SalesStatItem.fromMap(map)).toList();
  }

  // 기간 및 판매자별 "판매" 총액 합계
  Future<int> getTotalSalesAmount(
    int startTime,
    int endTime,
    String? sellerName,
  ) async {
    final db = await _database;
    String query =
        '''
      SELECT SUM(totalAmount) as total 
      FROM ${DatabaseServiceImpl.salesLogTable} 
      WHERE transactionType = ? 
      AND saleTimestamp BETWEEN ? AND ?
    ''';
    List<dynamic> args = [TransactionType.sale.value, startTime, endTime];

    if (sellerName != null) {
      query += ' AND sellerName = ?';
      args.add(sellerName);
    }

    final result = await db.rawQuery(query, args);
    return (result.first['total'] as int?) ?? 0;
  }

  // 모든 기간, 모든 판매자 "판매" 총액 합계
  Future<int> getTotalSalesAmountAll() async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount) as total 
      FROM ${DatabaseServiceImpl.salesLogTable} 
      WHERE transactionType = ?
    ''',
      [TransactionType.sale.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 특정 판매자의 모든 기간 "판매" 총액 합계
  Future<int> getTotalSalesAmountBySeller(String sellerName) async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount) as total 
      FROM ${DatabaseServiceImpl.salesLogTable} 
      WHERE sellerName = ? AND transactionType = ?
    ''',
      [sellerName, TransactionType.sale.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 기간 및 판매자별 "할인" 총액 합계
  Future<int> getTotalDiscountAmount(
    int startTime,
    int endTime,
    String? sellerName,
  ) async {
    final db = await _database;
    String query =
        '''
      SELECT SUM(totalAmount) as total 
      FROM ${DatabaseServiceImpl.salesLogTable} 
      WHERE transactionType = ? 
      AND saleTimestamp BETWEEN ? AND ?
    ''';
    List<dynamic> args = [TransactionType.discount.value, startTime, endTime];

    if (sellerName != null) {
      query += ' AND sellerName = ?';
      args.add(sellerName);
    }

    final result = await db.rawQuery(query, args);
    return (result.first['total'] as int?) ?? 0;
  }

  // 모든 기간, 모든 판매자 "할인" 총액 합계
  Future<int> getTotalDiscountAmountAll() async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount) as total 
      FROM ${DatabaseServiceImpl.salesLogTable} 
      WHERE transactionType = ?
    ''',
      [TransactionType.discount.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 특정 판매자의 모든 기간 "할인" 총액 합계
  Future<int> getTotalDiscountAmountBySeller(String sellerName) async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount) as total 
      FROM ${DatabaseServiceImpl.salesLogTable} 
      WHERE sellerName = ? AND transactionType = ?
    ''',
      [sellerName, TransactionType.discount.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }
} 