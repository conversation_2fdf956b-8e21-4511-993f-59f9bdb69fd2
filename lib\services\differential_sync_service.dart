/// 차분 동기화 서비스
/// 
/// Firebase와 로컬 데이터를 비교하여 변경된 부분만 동기화합니다.
/// DB 사용량을 최소화하고 효율적인 동기화를 제공합니다.
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/seller.dart';
import '../models/sales_log.dart';
import '../models/prepayment.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../services/database_service.dart';
import '../services/sync_metadata_service.dart';
import '../utils/logger_utils.dart';
import '../utils/network_status.dart';
import '../utils/merge_util.dart';

/// 차분 동기화 결과
class DiffSyncResult {
  final int downloaded;
  final int uploaded;
  final int deleted;
  final int skipped;
  final List<String> errors;

  const DiffSyncResult({
    required this.downloaded,
    required this.uploaded,
    required this.deleted,
    required this.skipped,
    required this.errors,
  });

  int get totalProcessed => downloaded + uploaded + deleted + skipped;
  bool get hasErrors => errors.isNotEmpty;
}

/// 안전한 원격 이벤트 가져오기 결과
class _SafeRemoteEventsResult {
  final bool success;
  final List<Event> events;
  final String? error;

  const _SafeRemoteEventsResult({
    required this.success,
    required this.events,
    this.error,
  });

  factory _SafeRemoteEventsResult.success(List<Event> events) {
    return _SafeRemoteEventsResult(success: true, events: events);
  }

  factory _SafeRemoteEventsResult.failure(String error) {
    return _SafeRemoteEventsResult(success: false, events: [], error: error);
  }
}

class DifferentialSyncService {
  static const String _tag = 'DifferentialSyncService';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DatabaseService _databaseService;
  final SyncMetadataService _metadataService = SyncMetadataService.instance;

  DifferentialSyncService(this._databaseService);

  /// 행사 목록 동기화 (메타데이터만) - 안전성 강화 버전
  Future<DiffSyncResult> syncEventsList({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('사용자 인증이 필요합니다');
    }

    try {
      LoggerUtils.logInfo('행사 목록 동기화 시작 (안전성 강화 버전)', tag: _tag);
      onProgress?.call('행사 목록 동기화 중...');

      // 1. 네트워크 상태 확인
      if (!NetworkStatusUtil.isOnline) {
        LoggerUtils.logWarning('네트워크 연결 없음 - 행사 목록 동기화 건너뛰기', tag: _tag);
        onError?.call('네트워크 연결이 필요합니다');
        return const DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 1, errors: ['네트워크 연결 없음']);
      }

      // 2. Firebase에서 행사 목록 가져오기 (안전한 방식)
      final remoteEventsResult = await _getRemoteEventsSafely(user.uid);
      if (!remoteEventsResult.success) {
        LoggerUtils.logError('원격 행사 목록 가져오기 실패: ${remoteEventsResult.error}', tag: _tag);
        onError?.call('서버에서 데이터를 가져올 수 없습니다: ${remoteEventsResult.error}');
        return DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 1, errors: [remoteEventsResult.error ?? '알 수 없는 오류']);
      }

      final remoteEvents = remoteEventsResult.events;
      LoggerUtils.logInfo('원격 행사 목록 가져오기 성공: ${remoteEvents.length}개', tag: _tag);

      // 3. 로컬 행사 목록 가져오기
      final localEvents = await _getLocalEvents();
      LoggerUtils.logInfo('로컬 행사 목록: ${localEvents.length}개', tag: _tag);

      int downloaded = 0;
      int deleted = 0;
      final List<String> errors = [];

      // 4. 안전한 삭제 처리 - 원격 데이터가 실제로 존재할 때만 삭제 수행
      if (remoteEvents.isNotEmpty || localEvents.isEmpty) {
        // 원격에 데이터가 있거나, 로컬에 데이터가 없는 경우에만 삭제 처리
        for (final localEvent in localEvents) {
          final isDeleted = !remoteEvents.any((remote) => remote.id == localEvent.id);
          if (isDeleted) {
            try {
              LoggerUtils.logInfo('행사 삭제 (안전 확인됨): ${localEvent.name} (ID: ${localEvent.id})', tag: _tag);
              await _databaseService.deleteEventAndAllData(localEvent.id!);
              deleted++;
            } catch (e) {
              errors.add('행사 ${localEvent.name} 삭제 실패: $e');
              LoggerUtils.logError('행사 삭제 실패: ${localEvent.name}', tag: _tag, error: e);
            }
          }
        }
      } else {
        // 원격 데이터가 비어있고 로컬에 데이터가 있는 경우 - 삭제하지 않음
        LoggerUtils.logWarning('원격 데이터가 비어있지만 로컬에 데이터 존재 - 안전을 위해 삭제하지 않음', tag: _tag);
        LoggerUtils.logWarning('로컬 행사 ${localEvents.length}개 보존됨', tag: _tag);
      }

      // 5. 새로운/업데이트된 행사 처리
      for (final remoteEvent in remoteEvents) {
        try {
          await _databaseService.insertOrUpdateEvent(remoteEvent);
          downloaded++;
          LoggerUtils.logDebug('행사 동기화 완료: ${remoteEvent.name}', tag: _tag);
        } catch (e) {
          errors.add('행사 ${remoteEvent.name} 동기화 실패: $e');
          LoggerUtils.logError('행사 동기화 실패: ${remoteEvent.name}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('행사 목록 동기화 완료 - 다운로드: $downloaded, 삭제: $deleted, 오류: ${errors.length}개', tag: _tag);

      return DiffSyncResult(
        downloaded: downloaded,
        uploaded: 0,
        deleted: deleted,
        skipped: 0,
        errors: errors,
      );
    } catch (e) {
      LoggerUtils.logError('행사 목록 동기화 실패', tag: _tag, error: e);
      onError?.call('행사 목록 동기화 실패: $e');
      rethrow;
    }
  }

  /// 현재 행사의 데이터만 차분 동기화 - 안전성 강화 버전
  Future<DiffSyncResult> syncCurrentEventData(
    int eventId, {
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('사용자 인증이 필요합니다');
    }

    try {
      LoggerUtils.logInfo('행사 $eventId 차분 동기화 시작 (안전성 강화 버전)', tag: _tag);
      onProgress?.call('동기화 준비 중...');

      // 네트워크 상태 확인
      if (!NetworkStatusUtil.isOnline) {
        LoggerUtils.logWarning('네트워크 연결 없음 - 차분 동기화 건너뛰기', tag: _tag);
        onError?.call('네트워크 연결이 필요합니다');
        return const DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 1, errors: ['네트워크 연결 없음']);
      }

      int totalDownloaded = 0;
      int totalUploaded = 0;
      int totalDeleted = 0;
      int totalSkipped = 0;
      final List<String> errors = [];
      
      // 동기화할 컬렉션들 (우선순위 순)
      final collections = [
        'categories',
        'products', 
        'sellers',
        'prepayments',
        'prepayment_virtual_products',
        'prepayment_product_links',
        'sales_logs',
      ];
      
      for (int i = 0; i < collections.length; i++) {
        final collection = collections[i];
        final progress = ((i + 1) / collections.length * 100).toInt();
        
        try {
          onProgress?.call('$collection 동기화 중... ($progress%)');
          
          final result = await _syncCollection(user.uid, eventId, collection);
          totalDownloaded += result.downloaded;
          totalUploaded += result.uploaded;
          totalDeleted += result.deleted;
          totalSkipped += result.skipped;
          errors.addAll(result.errors);
          
          LoggerUtils.logInfo(
            '$collection 동기화 완료: 다운로드=${result.downloaded}, 업로드=${result.uploaded}, 삭제=${result.deleted}, 스킵=${result.skipped}',
            tag: _tag
          );
        } catch (e) {
          final errorMsg = '$collection 동기화 실패: $e';
          LoggerUtils.logError(errorMsg, tag: _tag, error: e);
          errors.add(errorMsg);
          onError?.call(errorMsg);
        }
      }
      
      final result = DiffSyncResult(
        downloaded: totalDownloaded,
        uploaded: totalUploaded,
        deleted: totalDeleted,
        skipped: totalSkipped,
        errors: errors,
      );
      
      onProgress?.call('동기화 완료! (다운로드: $totalDownloaded, 업로드: $totalUploaded)');
      LoggerUtils.logInfo('행사 $eventId 차분 동기화 완료: $result', tag: _tag);
      
      return result;
    } catch (e) {
      LoggerUtils.logError('차분 동기화 실패', tag: _tag, error: e);
      onError?.call('동기화 실패: $e');
      rethrow;
    }
  }
  
  /// 특정 컬렉션의 차분 동기화 - 안전성 강화 버전
  Future<DiffSyncResult> _syncCollection(String userId, int eventId, String collectionName) async {
    try {
      LoggerUtils.logInfo('$collectionName 컬렉션 동기화 시작 (eventId: $eventId)', tag: _tag);

      // 1. 네트워크 상태 재확인
      if (!NetworkStatusUtil.isOnline) {
        LoggerUtils.logWarning('$collectionName: 네트워크 연결 없음 - 동기화 건너뛰기', tag: _tag);
        return const DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 1, errors: ['네트워크 연결 없음']);
      }

      // 2. 원격 데이터 개수 확인 (타임아웃 설정)
      final remoteCollection = _firestore
          .collection('users')
          .doc(userId)
          .collection('events')
          .doc(eventId.toString())
          .collection(collectionName);

      final remoteSnapshot = await remoteCollection.count().get().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException('컬렉션 개수 조회 타임아웃: $collectionName', const Duration(seconds: 15));
        },
      );
      final remoteCount = remoteSnapshot.count ?? 0;
      LoggerUtils.logDebug('$collectionName 원격 데이터 개수: $remoteCount', tag: _tag);

      // 3. 동기화 필요 여부 확인
      final needsSync = await _metadataService.needsSync(eventId, collectionName, remoteCount);

      if (!needsSync) {
        LoggerUtils.logInfo('$collectionName: 동기화 불필요 (메타데이터 기준)', tag: _tag);
        return const DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 1, errors: []);
      }

      // 4. 변경된 데이터만 가져오기
      final lastSync = await _metadataService.getLastSyncTime(eventId, collectionName) ??
                      DateTime.fromMillisecondsSinceEpoch(0);
      LoggerUtils.logDebug('$collectionName 마지막 동기화 시간: $lastSync', tag: _tag);

      // 5. Firebase에서 변경된 문서들 가져오기 (타임아웃 설정)
      final remoteQuery = _firestore
          .collection('users')
          .doc(userId)
          .collection('events')
          .doc(eventId.toString())
          .collection(collectionName)
          .where('updatedAt', isGreaterThan: Timestamp.fromDate(lastSync))
          .orderBy('updatedAt');

      final remoteDocuments = await remoteQuery.get().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('$collectionName 데이터 조회 타임아웃', const Duration(seconds: 30));
        },
      );

      LoggerUtils.logDebug('$collectionName 변경된 문서 수: ${remoteDocuments.docs.length}', tag: _tag);

      // 6. 로컬 데이터와 비교하여 처리
      int downloaded = 0;
      int uploaded = 0;
      int deleted = 0;
      final List<String> errors = [];

      for (final doc in remoteDocuments.docs) {
        try {
          await _processDocumentChange(eventId, collectionName, doc);
          downloaded++;
          LoggerUtils.logDebug('$collectionName 문서 처리 완료: ${doc.id}', tag: _tag);
        } catch (e) {
          final errorMsg = '문서 ${doc.id} 처리 실패: $e';
          errors.add(errorMsg);
          LoggerUtils.logError('$collectionName $errorMsg', tag: _tag, error: e);
        }
      }

      // 7. 마지막 동기화 시간 업데이트
      try {
        await _metadataService.updateLastSyncTime(
          eventId,
          collectionName,
          DateTime.now(),
          syncCount: remoteCount,
        );
        LoggerUtils.logDebug('$collectionName 동기화 메타데이터 업데이트 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('$collectionName 동기화 메타데이터 업데이트 실패', tag: _tag, error: e);
        errors.add('메타데이터 업데이트 실패: $e');
      }

      LoggerUtils.logInfo('$collectionName 동기화 완료 - 다운로드: $downloaded, 오류: ${errors.length}개', tag: _tag);

      return DiffSyncResult(
        downloaded: downloaded,
        uploaded: uploaded,
        deleted: deleted,
        skipped: 0,
        errors: errors,
      );
    } on TimeoutException catch (e) {
      LoggerUtils.logError('$collectionName 동기화 타임아웃', tag: _tag, error: e);
      return DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 0, errors: ['동기화 타임아웃: ${e.message}']);
    } on FirebaseException catch (e) {
      LoggerUtils.logError('$collectionName Firebase 오류', tag: _tag, error: e);
      return DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 0, errors: ['Firebase 오류: ${e.message}']);
    } catch (e) {
      LoggerUtils.logError('$collectionName 동기화 실패', tag: _tag, error: e);
      return DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 0, errors: ['동기화 실패: $e']);
    }
  }
  

  
  /// 문서 변경사항 처리
  Future<void> _processDocumentChange(int eventId, String collectionName, QueryDocumentSnapshot doc) async {
    final raw = doc.data() as Map<String, dynamic>;
    raw['id'] = int.parse(doc.id);
    raw['eventId'] = eventId; // 안전을 위해 행사 스코프 강제
    // 초기 병합: 로컬 미참조, 원격 기반 맵을 표준화 (향후 로컬과 병합 예정)
    final data = MergeUtil.mergeMaps({}, raw);

    switch (collectionName) {
      case 'categories':
        final category = Category.fromJson(data);
        await _databaseService.insertOrUpdateCategory(category);
        break;
      case 'products':
        final product = Product.fromJson(data);
        await _databaseService.insertOrUpdateProduct(product);
        break;
      case 'sellers':
        final seller = Seller.fromJson(data);
        await _databaseService.insertOrUpdateSeller(seller);
        break;
      case 'prepayments':
        final prepayment = Prepayment.fromJson(data);
        await _databaseService.insertOrUpdatePrepayment(prepayment);
        break;
      case 'prepayment_virtual_products':
        final virtualProduct = PrepaymentVirtualProduct.fromMap(data);
        await _databaseService.insertOrUpdatePrepaymentVirtualProduct(virtualProduct);
        break;
      case 'prepayment_product_links':
        final link = PrepaymentProductLink.fromMap(data);
        await _databaseService.insertOrUpdatePrepaymentProductLink(link);
        break;
      case 'sales_logs':
        final salesLog = SalesLog.fromJson(data);
        await _databaseService.insertOrUpdateSalesLog(salesLog);
        break;
    }
  }

  /// Firebase에서 행사 목록 가져오기 (안전한 버전)
  Future<_SafeRemoteEventsResult> _getRemoteEventsSafely(String userId) async {
    try {
      LoggerUtils.logInfo('Firebase에서 행사 목록 가져오기 시작', tag: _tag);

      // 네트워크 상태 재확인
      if (!NetworkStatusUtil.isOnline) {
        return _SafeRemoteEventsResult.failure('네트워크 연결 없음');
      }

      // 사용자 문서 존재 여부 먼저 확인
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        LoggerUtils.logWarning('사용자 문서가 존재하지 않음: $userId', tag: _tag);
        return _SafeRemoteEventsResult.failure('사용자 계정을 찾을 수 없습니다');
      }

      final eventsCollection = _firestore
          .collection('users')
          .doc(userId)
          .collection('events');

      // 타임아웃 설정으로 무한 대기 방지
      final snapshot = await eventsCollection.get().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('Firebase 쿼리 타임아웃', const Duration(seconds: 30));
        },
      );

      final events = <Event>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          events.add(Event.fromFirebaseMap(data));
        } catch (e) {
          LoggerUtils.logWarning('행사 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
          // 개별 문서 파싱 실패는 전체 실패로 처리하지 않음
        }
      }

      LoggerUtils.logInfo('Firebase에서 행사 목록 가져오기 성공: ${events.length}개', tag: _tag);
      return _SafeRemoteEventsResult.success(events);

    } on TimeoutException catch (e) {
      LoggerUtils.logError('Firebase 쿼리 타임아웃', tag: _tag, error: e);
      return _SafeRemoteEventsResult.failure('서버 응답 시간 초과');
    } on FirebaseException catch (e) {
      LoggerUtils.logError('Firebase 오류', tag: _tag, error: e);

      // Firebase 특정 오류 처리
      switch (e.code) {
        case 'permission-denied':
          return _SafeRemoteEventsResult.failure('접근 권한이 없습니다');
        case 'unavailable':
          return _SafeRemoteEventsResult.failure('서버에 연결할 수 없습니다');
        case 'deadline-exceeded':
          return _SafeRemoteEventsResult.failure('서버 응답 시간 초과');
        default:
          return _SafeRemoteEventsResult.failure('서버 오류: ${e.message}');
      }
    } catch (e) {
      LoggerUtils.logError('예상치 못한 오류', tag: _tag, error: e);
      return _SafeRemoteEventsResult.failure('알 수 없는 오류: ${e.toString()}');
    }
  }



  /// 로컬에서 행사 목록 가져오기
  Future<List<Event>> _getLocalEvents() async {
    final db = await _databaseService.database;
    final result = await db.query('events');

    return result.map((map) => Event.fromMap(map)).toList();
  }
}
