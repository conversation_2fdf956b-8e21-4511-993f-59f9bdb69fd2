# Blue Booth Manager - Local CI Script (PowerShell 7)
# Windows environment CI pipeline script

param(
    [switch]$SkipBuild,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Blue Booth Manager - Local CI Pipeline Started" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

$StartTime = Get-Date

function Write-LogInfo {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-LogSuccess {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-LogWarning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-LogError {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 1. Install dependencies
Write-LogInfo "1. Installing dependencies..."
flutter pub get
if ($LASTEXITCODE -eq 0) {
    Write-LogSuccess "Dependencies installed successfully"
} else {
    Write-LogError "Failed to install dependencies"
    exit 1
}

# 2. Code quality check
Write-LogInfo "2. Running code quality checks..."

# Code analysis
Write-LogInfo "  - Running code analysis..."
flutter analyze
if ($LASTEXITCODE -eq 0) {
    Write-LogSuccess "Code analysis completed"
} else {
    Write-LogError "Code analysis failed"
    exit 1
}

# Format check
Write-LogInfo "  - Checking code format..."
try {
    dart format --set-exit-if-changed .
    Write-LogSuccess "Code format check completed"
} catch {
    Write-LogWarning "Code format issues found. Run 'dart format .' to fix"
}

# Custom lint check
Write-LogInfo "  - Checking custom lint..."
if (Test-Path "custom_lint.log") {
    $lintContent = Get-Content "custom_lint.log" -Raw
    if ($lintContent -match "ERROR") {
        Write-LogError "Custom lint errors found:"
        Get-Content "custom_lint.log" | Where-Object { $_ -match "ERROR" }
        exit 1
    } else {
        Write-LogSuccess "Custom lint check completed"
    }
} else {
    Write-LogInfo "No custom lint log file found"
}

# 3. Run tests
Write-LogInfo "3. Running tests..."
flutter test --coverage
if ($LASTEXITCODE -eq 0) {
    Write-LogSuccess "Tests completed"
} else {
    Write-LogError "Tests failed"
    exit 1
}

# 4. Performance tests
Write-LogInfo "4. Running performance tests..."
flutter test test/utils/memory_manager_test.dart --verbose
flutter test test/utils/batch_processor_test.dart --verbose
Write-LogSuccess "Performance tests completed"

# 5. Build verification
Write-LogInfo "5. Build verification step..."
if (-not $SkipBuild) {
    Write-LogInfo "  - Building for web..."
    flutter build web --release
    if ($LASTEXITCODE -eq 0) {
        Write-LogSuccess "Web build completed"
    } else {
        Write-LogError "Web build failed"
        exit 1
    }
    
    Write-LogInfo "  - Building for Windows..."
    flutter build windows
    if ($LASTEXITCODE -eq 0) {
        Write-LogSuccess "Windows build completed"
    } else {
        Write-LogWarning "Windows build failed (continuing)"
    }
} else {
    Write-LogInfo "5. Build verification skipped (SkipBuild option)"
}

# 6. Security check
Write-LogInfo "6. Running security check..."
flutter pub deps --style=tree
Write-LogSuccess "Security check completed"

# Calculate duration
$EndTime = Get-Date
$Duration = ($EndTime - $StartTime).TotalSeconds

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-LogSuccess "🎉 Local CI Pipeline Completed!"
Write-Host "Total time: $([math]::Round($Duration, 2)) seconds" -ForegroundColor White
Write-Host ""
Write-Host "📊 Results Summary:" -ForegroundColor White
Write-Host "- ✅ Dependencies installed" -ForegroundColor Green
Write-Host "- ✅ Code quality checks" -ForegroundColor Green
Write-Host "- ✅ Tests executed" -ForegroundColor Green
Write-Host "- ✅ Performance tests" -ForegroundColor Green

if (-not $SkipBuild) {
    Write-Host "- ✅ Build verification" -ForegroundColor Green
} else {
    Write-Host "- ⏭️ Build verification (skipped)" -ForegroundColor Yellow
}

Write-Host "- ✅ Security check" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 All checks passed!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

# Usage guide
if ($Verbose) {
    Write-Host ""
    Write-Host "💡 Usage:" -ForegroundColor Cyan
    Write-Host "  .\scripts\ci_local.ps1              # Run full CI"
    Write-Host "  .\scripts\ci_local.ps1 -SkipBuild   # Skip build step"
    Write-Host "  .\scripts\ci_local.ps1 -Verbose     # Show detailed info"
} 