import 'package:flutter/material.dart';
import '../../models/prepayment.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import 'statistics_state.dart';

/// 통계 계산 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 판매 기록 필터링
/// - 통계 데이터 계산
/// - 선입금 총액 계산
/// - 성능 최적화된 집계 처리
class StatisticsCalculator {
  /// 필터링된 판매 기록 목록을 반환합니다.
  ///
  /// [allLogs]: 전체 판매 기록 목록
  /// [selectedSeller]: 선택된 판매자 (전체 판매자면 '전체 판매자')
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터링된 판매 기록 목록
  static List<SalesLog> getFilteredSalesLogs(
    List<SalesLog> allLogs, {
    required String selectedSeller,
    DateTimeRange? selectedDateRange,
  }) {
    return allLogs.where((log) {
      // 판매자 필터
      if (selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = selectedDateRange.start;
        final endDate = selectedDateRange.end.add(
          const Duration(days: 1),
        ); // 종료일 포함

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 선입금 총액을 계산합니다.
  ///
  /// [prepayments]: 선입금 목록
  /// 반환값: 선입금 총액 (항상 전체)
  static int calculateTotalPrepaymentAmount(List<Prepayment> prepayments) {
    return prepayments.fold(0, (sum, prepayment) => sum + prepayment.amount);
  }

  /// 판매 기록 목록에서 통계를 계산합니다.
  ///
  /// [logs]: 판매 기록 목록
  /// [productCategoryMap]: 상품ID -> 카테고리명 매핑 (선택사항)
  /// 반환값: 계산된 통계 데이터
  static SalesStats calculateStats(List<SalesLog> logs, {Map<int, String>? productCategoryMap}) {
    final stats = SalesStats();

    for (final log in logs) {
      stats.totalTransactions++;
      stats.totalQuantity += log.soldQuantity;
      stats.totalAmount += log.totalAmount;

      // 거래 유형별 통계
      final typeStats = stats.transactionTypeStats.putIfAbsent(
        log.transactionType,
        () => TransactionTypeStats(),
      );
      typeStats.count++;
      typeStats.quantity += log.soldQuantity;
      typeStats.amount += log.totalAmount;

      // 세트 할인 통계는 별도로 처리 (중복 방지를 위해)

      // 상품별 통계
      String productDisplayName = log.productName; // 기본적으로 원본 상품명 사용

      // 카테고리 정보가 있고 productId가 있는 경우 카테고리명 포함
      if (productCategoryMap != null && log.productId != null) {
        final categoryName = productCategoryMap[log.productId];
        if (categoryName != null) {
          productDisplayName = '[$categoryName]${log.productName}';
        }
      }

      final productStats = stats.productStats.putIfAbsent(
        productDisplayName,
        () => ProductStats(productName: productDisplayName),
      );
      productStats.count++;
      productStats.quantity += log.soldQuantity;
      productStats.amount += log.totalAmount;

      // 판매자별 통계
      final sellerStats = stats.sellerStats.putIfAbsent(
        log.sellerName ?? '알 수 없음',
        () => SellerStats(sellerName: log.sellerName ?? '알 수 없음'),
      );
      sellerStats.count++;
      sellerStats.quantity += log.soldQuantity;
      sellerStats.amount += log.totalAmount;
    }

    // 세트 할인 통계 별도 계산
    _calculateSetDiscountStats(logs, stats);

    return stats;
  }

  /// 세트 할인 통계 계산 (batchSaleId 기준으로 그룹핑)
  static void _calculateSetDiscountStats(List<SalesLog> logs, SalesStats stats) {
    // 세트 할인이 적용된 로그들만 필터링
    final setDiscountLogs = logs.where((log) => log.setDiscountAmount > 0).toList();

    if (setDiscountLogs.isEmpty) return;

    // batchSaleId별로 그룹핑 (같은 세트 판매)
    final Map<String?, List<SalesLog>> groupedByBatch = {};
    final Set<String> processedSingleItems = {}; // 단일 판매 중복 방지

    for (final log in setDiscountLogs) {
      if (log.batchSaleId != null) {
        // 묶음 판매인 경우
        groupedByBatch.putIfAbsent(log.batchSaleId, () => []).add(log);
      } else {
        // 단일 판매인 경우 (중복 방지)
        final key = '${log.id}_${log.saleTimestamp}';
        if (!processedSingleItems.contains(key)) {
          processedSingleItems.add(key);
          groupedByBatch.putIfAbsent(null, () => []).add(log);
        }
      }
    }

    final setDiscountStats = stats.transactionTypeStats.putIfAbsent(
      TransactionType.setDiscount,
      () => TransactionTypeStats(),
    );

    // 각 그룹(세트)별로 통계 계산
    for (final group in groupedByBatch.values) {
      if (group.isNotEmpty) {
        // 건수: 1건 (하나의 세트 판매)
        setDiscountStats.count++;

        // 수량: 해당 세트에 포함된 모든 상품의 수량 합계
        final totalQuantity = group.fold<int>(0, (sum, log) => sum + log.soldQuantity);
        setDiscountStats.quantity += totalQuantity;

        // 할인 금액: 해당 세트의 총 할인 금액
        final totalDiscountAmount = group.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
        setDiscountStats.amount += totalDiscountAmount;
      }
    }
  }

  /// 거래 유형별 통계를 정렬하여 반환합니다.
  ///
  /// [stats]: 통계 데이터
  /// [sortBy]: 정렬 기준 ('count', 'quantity', 'amount')
  /// [ascending]: 오름차순 여부
  /// 반환값: 정렬된 거래 유형별 통계 목록
  static List<MapEntry<TransactionType, TransactionTypeStats>> 
      getSortedTransactionTypeStats(
    SalesStats stats, {
    String sortBy = 'amount',
    bool ascending = false,
  }) {
    final entries = stats.transactionTypeStats.entries.toList();
    
    switch (sortBy) {
      case 'count':
        entries.sort((a, b) => ascending 
          ? a.value.count.compareTo(b.value.count)
          : b.value.count.compareTo(a.value.count));
        break;
      case 'quantity':
        entries.sort((a, b) => ascending 
          ? a.value.quantity.compareTo(b.value.quantity)
          : b.value.quantity.compareTo(a.value.quantity));
        break;
      case 'amount':
      default:
        entries.sort((a, b) => ascending 
          ? a.value.amount.compareTo(b.value.amount)
          : b.value.amount.compareTo(a.value.amount));
        break;
    }
    
    return entries;
  }

  /// 상품별 통계를 정렬하여 반환합니다.
  ///
  /// [stats]: 통계 데이터
  /// [sortBy]: 정렬 기준 ('count', 'quantity', 'amount')
  /// [ascending]: 오름차순 여부
  /// 반환값: 정렬된 상품별 통계 목록
  static List<MapEntry<String, ProductStats>> getSortedProductStats(
    SalesStats stats, {
    String sortBy = 'amount',
    bool ascending = false,
  }) {
    final entries = stats.productStats.entries.toList();
    
    switch (sortBy) {
      case 'count':
        entries.sort((a, b) => ascending 
          ? a.value.count.compareTo(b.value.count)
          : b.value.count.compareTo(a.value.count));
        break;
      case 'quantity':
        entries.sort((a, b) => ascending 
          ? a.value.quantity.compareTo(b.value.quantity)
          : b.value.quantity.compareTo(a.value.quantity));
        break;
      case 'amount':
      default:
        entries.sort((a, b) => ascending 
          ? a.value.amount.compareTo(b.value.amount)
          : b.value.amount.compareTo(a.value.amount));
        break;
    }
    
    return entries;
  }

  /// 판매자별 통계를 정렬하여 반환합니다.
  ///
  /// [stats]: 통계 데이터
  /// [sortBy]: 정렬 기준 ('count', 'quantity', 'amount')
  /// [ascending]: 오름차순 여부
  /// 반환값: 정렬된 판매자별 통계 목록
  static List<MapEntry<String, SellerStats>> getSortedSellerStats(
    SalesStats stats, {
    String sortBy = 'amount',
    bool ascending = false,
  }) {
    final entries = stats.sellerStats.entries.toList();
    
    switch (sortBy) {
      case 'count':
        entries.sort((a, b) => ascending 
          ? a.value.count.compareTo(b.value.count)
          : b.value.count.compareTo(a.value.count));
        break;
      case 'quantity':
        entries.sort((a, b) => ascending 
          ? a.value.quantity.compareTo(b.value.quantity)
          : b.value.quantity.compareTo(a.value.quantity));
        break;
      case 'amount':
      default:
        entries.sort((a, b) => ascending 
          ? a.value.amount.compareTo(b.value.amount)
          : b.value.amount.compareTo(a.value.amount));
        break;
    }
    
    return entries;
  }
} 