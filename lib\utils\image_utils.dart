import 'dart:typed_data';
import 'package:image/image.dart' as img;

/// 3단계 방식: 흰색 1000x1000 캔버스에, 긴 변이 800px 이하로 리사이즈한 이미지를 중앙 배치
/// - 입력: 원본 이미지 바이트
/// - 출력: 1000x1000 흰색 배경에 중앙 배치된 이미지 바이트(JPG)
Future<Uint8List> addWhitePaddingAndCenterImage(Uint8List originalBytes) async {
  const int canvasSize = 800;
  const int maxImageSize = 650;
  // 원본 이미지 디코딩
  final img.Image? original = img.decodeImage(originalBytes);
  if (original == null) throw Exception('이미지 디코딩 실패');

  // 긴 변이 maxImageSize를 넘으면 비율 유지 리사이즈
  final int origW = original.width;
  final int origH = original.height;
  final int longSide = origW > origH ? origW : origH;
  double scale = 1.0;
  if (longSide > maxImageSize) {
    scale = maxImageSize / longSide;
  }
  final int newW = (origW * scale).round();
  final int newH = (origH * scale).round();
  final img.Image resized = (scale < 1.0)
      ? img.copyResize(original, width: newW, height: newH)
      : original;

  // 흰색 캔버스 생성
  final img.Image canvas = img.Image(width: canvasSize, height: canvasSize);
  img.fill(canvas, color: img.ColorUint8.rgb(255, 255, 255)); // 흰색

  // 중앙 배치 좌표 계산
  final int offsetX = ((canvasSize - newW) / 2).round();
  final int offsetY = ((canvasSize - newH) / 2).round();

  // 캔버스에 이미지 합성
  img.compositeImage(canvas, resized, dstX: offsetX, dstY: offsetY);

  // JPG로 인코딩 (quality: 80으로 용량 최적화)
  return Uint8List.fromList(img.encodeJpg(canvas, quality: 80));
} 