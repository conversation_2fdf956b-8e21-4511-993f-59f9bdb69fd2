import 'package:flutter/material.dart';
import '../services/pdf_export_service.dart';
import '../utils/app_colors.dart';
import '../utils/dialog_theme.dart' as custom_dialog;

class PdfExportTypeSelectionDialog extends StatefulWidget {
  const PdfExportTypeSelectionDialog({super.key});

  @override
  State<PdfExportTypeSelectionDialog> createState() => _PdfExportTypeSelectionDialogState();
}

class _PdfExportTypeSelectionDialogState extends State<PdfExportTypeSelectionDialog> {
  PdfExportType _selectedType = PdfExportType.summary;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    return custom_dialog.DialogTheme.buildResponsiveDialog(
      isCompact: true,
      child: Container(
        padding: EdgeInsets.all(isTablet ? 24 : 18),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  color: AppColors.primarySeed,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'PDF 내보내기 유형 선택',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primarySeed,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            Text(
              '생성할 PDF 리포트의 유형을 선택해주세요.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 선택 옵션들
            _buildOptionCard(
              type: PdfExportType.summary,
              title: '📋 요약 리포트',
              subtitle: '핵심 지표 및 TOP 10 상품',
              description: '• 빠른 생성 (2-3페이지)\n• 주요 매출 지표\n• 인기 상품 TOP 10\n• 간단한 분석 결과',
            ),
            
            const SizedBox(height: 16),
            
            _buildOptionCard(
              type: PdfExportType.detailed,
              title: '📊 상세 리포트',
              subtitle: '모든 상품별 판매 현황',
              description: '• 완전한 데이터 (5-10페이지)\n• 전체 상품 목록\n• 상세 통계 분석\n• 종합적인 인사이트',
            ),
            
            const SizedBox(height: 32),
            
            // 버튼들
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    '취소',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(_selectedType),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: Text(
                    '미리보기 생성',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOptionCard({
    required PdfExportType type,
    required String title,
    required String subtitle,
    required String description,
  }) {
    final isSelected = _selectedType == type;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primarySeed : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? AppColors.primarySeed.withValues(alpha: 0.05) : Colors.white,
        ),
        child: Row(
          children: [
            Radio<PdfExportType>(
              value: type,
              groupValue: _selectedType,
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
              activeColor: AppColors.primarySeed,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? AppColors.primarySeed : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      )
    );
  }
}
