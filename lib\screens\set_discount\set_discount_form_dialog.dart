import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/set_discount.dart';
import '../../providers/set_discount_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/currency_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';
import '../../utils/product_display_utils.dart';

/// 세트 할인 등록/수정 다이얼로그
class SetDiscountFormDialog extends ConsumerStatefulWidget {
  final SetDiscount? setDiscount; // null이면 새 등록, 값이 있으면 수정
  final Function(SetDiscount)? onSave;

  const SetDiscountFormDialog({
    super.key,
    this.setDiscount,
    this.onSave,
  });

  @override
  ConsumerState<SetDiscountFormDialog> createState() => _SetDiscountFormDialogState();
}

class _SetDiscountFormDialogState extends ConsumerState<SetDiscountFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _discountAmountController = TextEditingController();
  final _minimumAmountController = TextEditingController();
  final _categoryQuantityController = TextEditingController();
  final _productGroupQuantityController = TextEditingController();
  final _searchController = TextEditingController();

  // 할인 조건 타입
  SetDiscountConditionType _conditionType = SetDiscountConditionType.productCombination;

  // 상품 조합 할인용
  final Set<int> _selectedProductIds = <int>{};

  // 카테고리별 수량 할인용
  int? _selectedCategoryId;

  // 상품군 수량 할인용
  final Set<int> _selectedProductGroupIds = <int>{};

  // 검색 기능용
  String _searchQuery = '';

  // 반복 적용 여부
  bool _allowMultipleApplications = false;

  bool _isLoading = false;
  String? _errorMessage;

  bool get _isEditing => widget.setDiscount != null;

  @override
  void initState() {
    super.initState();
    
    // 수정 모드인 경우 기존 값으로 초기화
    if (_isEditing) {
      final setDiscount = widget.setDiscount!;
      _nameController.text = setDiscount.name;
      _discountAmountController.text = setDiscount.discountAmount.toString();
      _conditionType = setDiscount.conditionType;
      _allowMultipleApplications = setDiscount.allowMultipleApplications;

      switch (setDiscount.conditionType) {
        case SetDiscountConditionType.productCombination:
          _selectedProductIds.addAll(setDiscount.productIds);
          break;
        case SetDiscountConditionType.minimumAmount:
          _minimumAmountController.text = setDiscount.minimumAmount.toString();
          break;
        case SetDiscountConditionType.categoryQuantity:
          if (setDiscount.categoryCondition != null) {
            _selectedCategoryId = setDiscount.categoryCondition!.categoryId;
            _categoryQuantityController.text = setDiscount.categoryCondition!.minimumQuantity.toString();
          }
          break;
        case SetDiscountConditionType.productGroupQuantity:
          if (setDiscount.productGroupCondition != null) {
            _selectedProductGroupIds.addAll(setDiscount.productGroupCondition!.productIds);
            _productGroupQuantityController.text = setDiscount.productGroupCondition!.minimumQuantity.toString();
          }
          break;
      }
    }

    // 상품 목록 및 카테고리 목록 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
      ref.read(categoryNotifierProvider.notifier).loadCategories();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _discountAmountController.dispose();
    _minimumAmountController.dispose();
    _categoryQuantityController.dispose();
    _productGroupQuantityController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productState = ref.watch(productNotifierProvider);
    final products = productState.products;

    // 상품 데이터가 로드되지 않은 경우 로딩 표시
    if (productState.isLoading && products.isEmpty) {
      return Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(20),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('상품 데이터를 로드하는 중...'),
              ],
            ),
          ),
        ),
      );
    }

    // 상품이 없는 경우 에러 표시
    if (products.isEmpty) {
      return Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.warning, size: 48, color: Colors.orange),
              const SizedBox(height: 16),
              const Text('등록된 상품이 없습니다.'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('닫기'),
              ),
            ],
          ),
        ),
      );
    }

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // 헤더 (체크리스트 편집 다이얼로그 스타일)
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: '뒤로가기',
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isEditing ? '세트 할인 수정' : '세트 할인 등록',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.onSurface,
                    ),
                  ),
                  const Spacer(),
                  if (_isLoading)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                      ),
                    )
                  else
                    IconButton(
                      icon: const Icon(Icons.check, color: AppColors.primarySeed),
                      onPressed: _handleSubmit,
                      tooltip: '저장',
                    ),
                ],
              ),
            ),

            // 본문 - Expanded 안에 ListView 사용
            Expanded(
              child: Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  children: [
              // 세트 이름 입력
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: '세트 이름',
                  hintText: '예: 커플 세트, 패밀리 세트',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '세트 이름을 입력해주세요';
                  }
                  if (value.trim().length > 50) {
                    return '세트 이름은 50자 이내로 입력해주세요';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 할인 금액 입력
              TextFormField(
                controller: _discountAmountController,
                decoration: const InputDecoration(
                  labelText: '할인 금액',
                  hintText: '원',
                  border: OutlineInputBorder(),
                  suffixText: '원',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '할인 금액을 입력해주세요';
                  }
                  final amount = int.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return '올바른 할인 금액을 입력해주세요';
                  }
                  if (amount > 1000000) {
                    return '할인 금액은 100만원 이하로 입력해주세요';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 할인 조건 선택
              const Text(
                '할인 조건',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<SetDiscountConditionType>(
                value: _conditionType,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: '할인 조건을 선택하세요',
                ),
                items: const [
                  DropdownMenuItem(
                    value: SetDiscountConditionType.productCombination,
                    child: Text('상품 조합 할인'),
                  ),
                  DropdownMenuItem(
                    value: SetDiscountConditionType.minimumAmount,
                    child: Text('최소 구매 금액'),
                  ),
                  DropdownMenuItem(
                    value: SetDiscountConditionType.categoryQuantity,
                    child: Text('카테고리별 수량 할인'),
                  ),
                  DropdownMenuItem(
                    value: SetDiscountConditionType.productGroupQuantity,
                    child: Text('지정상품 중 수량 할인'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _conditionType = value;
                      // 조건 타입 변경 시 관련 데이터 초기화
                      _selectedProductIds.clear();
                      _selectedCategoryId = null;
                      _selectedProductGroupIds.clear();
                      _minimumAmountController.clear();
                      _categoryQuantityController.clear();
                      _productGroupQuantityController.clear();
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // 조건별 설정 UI
              _buildConditionSpecificUI(),
              const SizedBox(height: 16),
              const SizedBox(height: 8),

              // 에러 메시지 표시
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(8),
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
                    ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 폼 제출 처리
  void _handleSubmit() async {
    // 폼 유효성 검사
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final name = _nameController.text.trim();

      // 이름 유효성 검사
      if (name.isEmpty) {
        setState(() {
          _errorMessage = '세트 이름을 입력해주세요';
          _isLoading = false;
        });
        return;
      }

      // 할인 금액 파싱 및 유효성 검사
      int discountAmount;
      try {
        discountAmount = int.parse(_discountAmountController.text);
        if (discountAmount <= 0) {
          setState(() {
            _errorMessage = '할인 금액은 0보다 커야 합니다';
            _isLoading = false;
          });
          return;
        }
      } catch (e) {
        setState(() {
          _errorMessage = '올바른 할인 금액을 입력해주세요';
          _isLoading = false;
        });
        return;
      }

      // 할인 조건별 유효성 검사
      switch (_conditionType) {
        case SetDiscountConditionType.productCombination:
          // 특정 상품 조합 할인: 상품 선택 필수 (최소 2개)
          if (_selectedProductIds.isEmpty) {
            setState(() {
              _errorMessage = '최소 1개 이상의 상품을 선택해주세요';
              _isLoading = false;
            });
            return;
          }
          if (_selectedProductIds.length < 2) {
            setState(() {
              _errorMessage = '세트 할인은 최소 2개 이상의 상품이 필요합니다';
              _isLoading = false;
            });
            return;
          }
          break;
        case SetDiscountConditionType.minimumAmount:
          // 최소 구매 금액 할인: 상품 선택 불필요, 최소 구매 금액만 검사
          final minimumAmount = int.tryParse(_minimumAmountController.text);
          if (minimumAmount == null || minimumAmount <= 0) {
            setState(() {
              _errorMessage = '올바른 최소 구매 금액을 입력해주세요';
              _isLoading = false;
            });
            return;
          }
          break;
        case SetDiscountConditionType.categoryQuantity:
          // 카테고리별 수량 할인: 상품 선택 불필요, 카테고리와 조건 개수만 검사
          if (_selectedCategoryId == null) {
            setState(() {
              _errorMessage = '카테고리를 선택해주세요';
              _isLoading = false;
            });
            return;
          }
          final categoryQuantity = int.tryParse(_categoryQuantityController.text);
          if (categoryQuantity == null || categoryQuantity <= 0) {
            setState(() {
              _errorMessage = '올바른 조건 개수를 입력해주세요';
              _isLoading = false;
            });
            return;
          }
          break;
        case SetDiscountConditionType.productGroupQuantity:
          // 상품군 수량 할인: 상품 선택 필수, 조건 개수도 검사
          if (_selectedProductGroupIds.isEmpty) {
            setState(() {
              _errorMessage = '최소 1개 이상의 상품을 선택해주세요';
              _isLoading = false;
            });
            return;
          }
          final productGroupQuantity = int.tryParse(_productGroupQuantityController.text);
          if (productGroupQuantity == null || productGroupQuantity <= 0) {
            setState(() {
              _errorMessage = '올바른 조건 개수를 입력해주세요';
              _isLoading = false;
            });
            return;
          }
          break;
      }

      // 현재 이벤트 ID 확인 - null이면 에러 처리
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        setState(() {
          _errorMessage = '현재 행사가 선택되지 않았습니다. 행사를 선택한 후 다시 시도해주세요.';
          _isLoading = false;
        });
        return;
      }
      final currentEventId = currentWorkspace.id;

      LoggerUtils.logInfo('세트 할인 생성 시작: name=$name, discountAmount=$discountAmount, eventId=$currentEventId, conditionType=$_conditionType', tag: 'SetDiscountFormDialog');

      // 이름 중복 검사
      final nameExists = await ref
          .read(setDiscountNotifierProvider.notifier)
          .isNameExists(name, excludeId: widget.setDiscount?.id);

      if (nameExists) {
        setState(() {
          _errorMessage = '이미 존재하는 세트 이름입니다';
          _isLoading = false;
        });
        return;
      }

      // 할인 조건별 데이터 준비
      CategoryQuantityCondition? categoryCondition;
      ProductGroupQuantityCondition? productGroupCondition;
      int minimumAmount = 0;
      List<int> productIds = [];

      switch (_conditionType) {
        case SetDiscountConditionType.productCombination:
          productIds = _selectedProductIds.toList();
          break;
        case SetDiscountConditionType.minimumAmount:
          minimumAmount = int.parse(_minimumAmountController.text);
          break;
        case SetDiscountConditionType.categoryQuantity:
          categoryCondition = CategoryQuantityCondition(
            categoryId: _selectedCategoryId!,
            minimumQuantity: int.parse(_categoryQuantityController.text),
          );
          break;
        case SetDiscountConditionType.productGroupQuantity:
          productGroupCondition = ProductGroupQuantityCondition(
            productIds: _selectedProductGroupIds.toList(),
            minimumQuantity: int.parse(_productGroupQuantityController.text),
          );
          break;
      }

      final setDiscount = SetDiscount.create(
        id: widget.setDiscount?.id,
        name: name,
        discountAmount: discountAmount,
        conditionType: _conditionType,
        productIds: productIds,
        minimumAmount: minimumAmount,
        categoryCondition: categoryCondition,
        productGroupCondition: productGroupCondition,
        allowMultipleApplications: _allowMultipleApplications,
        eventId: currentEventId,
        createdAt: widget.setDiscount?.createdAt,
      );

      LoggerUtils.logInfo('SetDiscount 객체 생성 완료: $setDiscount', tag: 'SetDiscountFormDialog');

      // onSave 콜백 호출 (메모리에 추가)
      if (widget.onSave != null) {
        widget.onSave!(setDiscount);
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      LoggerUtils.logError('Failed to save set discount', error: e, tag: 'SetDiscountFormDialog');
      setState(() {
        _errorMessage = '오류가 발생했습니다: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// 할인 조건 타입별 UI 빌드
  Widget _buildConditionSpecificUI() {
    switch (_conditionType) {
      case SetDiscountConditionType.productCombination:
        return _buildProductCombinationUI();
      case SetDiscountConditionType.minimumAmount:
        return _buildMinimumAmountUI();
      case SetDiscountConditionType.categoryQuantity:
        return _buildCategoryQuantityUI();
      case SetDiscountConditionType.productGroupQuantity:
        return _buildProductGroupQuantityUI();
    }
  }

  /// 상품 조합 할인 UI
  Widget _buildProductCombinationUI() {
    final productState = ref.watch(productNotifierProvider);
    final categoriesAsyncValue = ref.watch(categoryNotifierProvider);
    final products = productState.products;

    // 검색어로 상품 필터링
    final filteredProducts = products.where((product) {
      if (_searchQuery.isEmpty) return true;
      return product.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '포함할 상품 선택',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // 검색 필드
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '상품명으로 검색...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        const SizedBox(height: 16),

        // 상품 목록
        Container(
          height: 350, // 높이 증가
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: filteredProducts.isEmpty
              ? Center(
                  child: Text(
                    _searchQuery.isEmpty ? '등록된 상품이 없습니다' : '검색 결과가 없습니다',
                    style: const TextStyle(color: Colors.grey),
                  ),
                )
              : categoriesAsyncValue.when(
                  data: (categories) => ListView.builder(
                    itemCount: filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = filteredProducts[index];
                      final isSelected = _selectedProductIds.contains(product.id);

                      // 상품명을 [카테고리][이름] 형식으로 표시
                      final displayName = ProductDisplayUtils.getDisplayNameWithCategory(product, categories);

                      return CheckboxListTile(
                        title: Text(displayName),
                        subtitle: Text(
                          '${CurrencyUtils.formatCurrency(product.price)} • 재고: ${product.quantity}개',
                        ),
                        value: isSelected,
                        onChanged: (bool? value) {
                          setState(() {
                            if (value == true) {
                              _selectedProductIds.add(product.id!);
                            } else {
                              _selectedProductIds.remove(product.id!);
                            }
                            _errorMessage = null; // 에러 메시지 클리어
                          });
                        },
                        dense: true,
                      );
                    },
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text(
                      '카테고리 로드 실패: $error',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  /// 최소 구매 금액 할인 UI
  Widget _buildMinimumAmountUI() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '최소 구매 금액 설정',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _minimumAmountController,
          decoration: const InputDecoration(
            labelText: '최소 구매 금액',
            hintText: '예: 50000',
            border: OutlineInputBorder(),
            suffixText: '원',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          validator: (value) {
            if (_conditionType == SetDiscountConditionType.minimumAmount) {
              if (value == null || value.trim().isEmpty) {
                return '최소 구매 금액을 입력해주세요';
              }
              final amount = int.tryParse(value);
              if (amount == null || amount <= 0) {
                return '올바른 금액을 입력해주세요';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 카테고리별 수량 할인 UI
  Widget _buildCategoryQuantityUI() {
    final categoriesAsyncValue = ref.watch(categoryNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '카테고리별 수량 조건 설정',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // 카테고리 선택 드롭다운
        const Text(
          '카테고리 선택',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        categoriesAsyncValue.when(
          data: (categories) {
            if (categories.isEmpty) {
              return const Text(
                '등록된 카테고리가 없습니다',
                style: TextStyle(color: Colors.grey),
              );
            }

            return DropdownButtonFormField<int>(
              value: _selectedCategoryId,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: '카테고리를 선택하세요',
              ),
              items: categories.map((category) {
                return DropdownMenuItem<int>(
                  value: category.id,
                  child: Text(category.name),
                );
              }).toList(),
              onChanged: (int? value) {
                setState(() {
                  _selectedCategoryId = value;
                  _errorMessage = null; // 에러 메시지 클리어
                });
              },
              validator: (value) {
                if (value == null) {
                  return '카테고리를 선택해주세요';
                }
                return null;
              },
            );
          },
          loading: () => const CircularProgressIndicator(),
          error: (error, stack) => Text(
            '카테고리 로드 실패: $error',
            style: const TextStyle(color: Colors.red),
          ),
        ),

        const SizedBox(height: 16),

        // 조건 개수 입력
        const Text(
          '조건 개수',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _categoryQuantityController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '조건 개수를 입력하세요',
            suffixText: '개',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '조건 개수를 입력해주세요';
            }
            final quantity = int.tryParse(value);
            if (quantity == null || quantity <= 0) {
              return '올바른 개수를 입력해주세요';
            }
            return null;
          },
          onChanged: (value) {
            setState(() {
              _errorMessage = null; // 에러 메시지 클리어
            });
          },
        ),

        const SizedBox(height: 16),

        // 반복 적용 여부 체크박스
        CheckboxListTile(
          title: const Text('반복 적용'),
          subtitle: const Text('조건을 만족할 때마다 할인을 반복 적용합니다\n(예: 2개 조건에 4개 구매 시 할인 2회 적용)'),
          value: _allowMultipleApplications,
          onChanged: (bool? value) {
            setState(() {
              _allowMultipleApplications = value ?? false;
            });
          },
          dense: true,
        ),
      ],
    );
  }

  /// 상품군 수량 할인 UI
  Widget _buildProductGroupQuantityUI() {
    final productState = ref.watch(productNotifierProvider);
    final categoriesAsyncValue = ref.watch(categoryNotifierProvider);
    final products = productState.products;

    // 검색어로 상품 필터링
    final filteredProducts = products.where((product) {
      if (_searchQuery.isEmpty) return true;
      return product.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '지정상품 중 수량 할인',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // 조건 개수 입력
        const Text(
          '조건 개수',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _productGroupQuantityController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '조건 개수를 입력하세요',
            suffixText: '개',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '조건 개수를 입력해주세요';
            }
            final quantity = int.tryParse(value);
            if (quantity == null || quantity <= 0) {
              return '올바른 개수를 입력해주세요';
            }
            return null;
          },
          onChanged: (value) {
            setState(() {
              _errorMessage = null; // 에러 메시지 클리어
            });
          },
        ),

        const SizedBox(height: 16),

        // 포함할 상품 선택
        const Text(
          '포함할 상품 선택',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),

        // 검색 필드
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '상품명으로 검색...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        const SizedBox(height: 8),

        // 상품 목록
        Container(
          height: 300, // 높이 증가
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: filteredProducts.isEmpty
              ? Center(
                  child: Text(
                    _searchQuery.isEmpty ? '등록된 상품이 없습니다' : '검색 결과가 없습니다',
                    style: const TextStyle(color: Colors.grey),
                  ),
                )
              : categoriesAsyncValue.when(
                  data: (categories) => ListView.builder(
                    itemCount: filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = filteredProducts[index];
                      final isSelected = _selectedProductGroupIds.contains(product.id);

                      // 상품명을 [카테고리][이름] 형식으로 표시
                      final displayName = ProductDisplayUtils.getDisplayNameWithCategory(product, categories);

                      return CheckboxListTile(
                        title: Text(displayName),
                        subtitle: Text(
                          '${CurrencyUtils.formatCurrency(product.price)} • 재고: ${product.quantity}개',
                        ),
                        value: isSelected,
                        onChanged: (bool? value) {
                          setState(() {
                            if (value == true) {
                              _selectedProductGroupIds.add(product.id!);
                            } else {
                              _selectedProductGroupIds.remove(product.id!);
                            }
                            _errorMessage = null; // 에러 메시지 클리어
                          });
                        },
                        dense: true,
                      );
                    },
                  ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text(
                      '카테고리 로드 실패: $error',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ),
        ),

        const SizedBox(height: 16),

        // 반복 적용 여부 체크박스
        CheckboxListTile(
          title: const Text('반복 적용'),
          subtitle: const Text('조건을 만족할 때마다 할인을 반복 적용합니다\n(예: 2개 조건에 4개 구매 시 할인 2회 적용)'),
          value: _allowMultipleApplications,
          onChanged: (bool? value) {
            setState(() {
              _allowMultipleApplications = value ?? false;
            });
          },
          dense: true,
        ),
      ],
    );
  }
}
