#!/bin/bash

# Blue Booth Manager - Local CI Script
# 로컬 개발 환경에서 CI 파이프라인을 실행하는 스크립트

set -e  # 오류 발생 시 스크립트 중단

echo "🚀 Blue Booth Manager - Local CI Pipeline 시작"
echo "================================================"

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 로그 함수
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 시작 시간 기록
START_TIME=$(date +%s)

# 1. 의존성 설치
log_info "1. 의존성 설치 중..."
flutter pub get
log_success "의존성 설치 완료"

# 2. 코드 품질 검사
log_info "2. 코드 품질 검사 중..."

# 코드 분석
log_info "  - 코드 분석 실행..."
flutter analyze
log_success "코드 분석 완료"

# 포맷 검사
log_info "  - 코드 포맷 검사..."
if dart format --set-exit-if-changed .; then
    log_success "코드 포맷 검사 완료"
else
    log_warning "코드 포맷 문제 발견. 'dart format .' 명령으로 수정하세요."
fi

# 커스텀 린트 검사
log_info "  - 커스텀 린트 검사..."
if [ -f "custom_lint.log" ]; then
    if grep -q "ERROR" custom_lint.log; then
        log_error "커스텀 린트 오류 발견:"
        grep "ERROR" custom_lint.log
        exit 1
    else
        log_success "커스텀 린트 검사 완료"
    fi
else
    log_info "커스텀 린트 로그 파일이 없습니다."
fi

# 3. 테스트 실행
log_info "3. 테스트 실행 중..."
flutter test --coverage
log_success "테스트 완료"

# 4. 성능 테스트
log_info "4. 성능 테스트 실행 중..."
flutter test test/utils/memory_manager_test.dart --verbose
flutter test test/utils/batch_processor_test.dart --verbose
log_success "성능 테스트 완료"

# 5. 빌드 검증
log_info "5. 빌드 검증 중..."

# 웹 빌드
log_info "  - 웹 빌드..."
flutter build web --release
log_success "웹 빌드 완료"

# Windows 빌드 (Windows 환경에서만)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    log_info "  - Windows 빌드..."
    flutter build windows
    log_success "Windows 빌드 완료"
fi

# 6. 보안 검사
log_info "6. 보안 검사 중..."
flutter pub deps --style=tree
log_success "보안 검사 완료"

# 종료 시간 및 소요 시간 계산
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "================================================"
log_success "🎉 Local CI Pipeline 완료!"
echo "총 소요 시간: ${DURATION}초"
echo ""
echo "📊 결과 요약:"
echo "- ✅ 의존성 설치"
echo "- ✅ 코드 품질 검사"
echo "- ✅ 테스트 실행"
echo "- ✅ 성능 테스트"
echo "- ✅ 빌드 검증"
echo "- ✅ 보안 검사"
echo ""
echo "🚀 모든 검사가 통과했습니다!"
echo "================================================" 