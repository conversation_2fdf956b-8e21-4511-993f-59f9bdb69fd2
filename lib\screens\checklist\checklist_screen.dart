import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/toast_utils.dart';
import '../../providers/checklist_provider.dart';
import '../../widgets/unsaved_changes_dialog.dart';
import '../../providers/unified_workspace_provider.dart';

import 'checklist_edit_dialog.dart';

/// 체크리스트 화면 - 실제 기능 버전
class ChecklistScreen extends ConsumerStatefulWidget {
  const ChecklistScreen({super.key});

  @override
  ConsumerState<ChecklistScreen> createState() => _ChecklistScreenState();
}

class _ChecklistScreenState extends ConsumerState<ChecklistScreen> {
  @override
  void initState() {
    super.initState();

    // 페이지 진입 시 체크리스트 데이터 로드 (AppWrapper 없이 직접 로드)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(checklistNotifierProvider.notifier).loadData();
    });
  }

  /// 편집 다이얼로그 표시
  void _showEditDialog() {
    showDialog(
      context: context,
      builder: (context) => const ChecklistEditDialog(),
    ).then((_) {
      // 다이얼로그 닫힌 후 데이터 새로고침
      ref.read(checklistNotifierProvider.notifier).loadData();
    });
  }

  /// 체크리스트 아이템 토글 (로컬 상태만 변경)
  void _toggleChecklistItem(int templateId) {
    ref.read(checklistNotifierProvider.notifier).toggleItemCheckLocal(templateId);
  }

  /// 변경사항 저장
  void _saveChanges() async {
    try {
      await ref.read(checklistNotifierProvider.notifier).saveChanges();
      if (mounted) {
        ToastUtils.showSuccess(context, '체크리스트가 저장되었습니다.');
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '저장 중 오류가 발생했습니다: $e');
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    print('ChecklistScreen build 시작');
    final checklistState = ref.watch(checklistNotifierProvider);

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final hasChanges = ref.read(checklistNotifierProvider).hasChanges;
          if (hasChanges) {
            final confirmed = await UnsavedChangesDialog.show(
              context: context,
            );
            if (confirmed != true || !mounted) return;
          }
          Navigator.of(context).maybePop();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.onSurface),
          onPressed: () async {
            final hasChanges = ref.read(checklistNotifierProvider).hasChanges;
            if (hasChanges) {
              final confirmed = await UnsavedChangesDialog.show(
                context: context,
              );
              if (confirmed == true && mounted) {
                Navigator.of(context).pop();
              }
            } else {
              if (mounted) Navigator.of(context).pop();
            }
          },
        ),
        title: const Text(
          '준비 체크리스트',
          style: TextStyle(
            color: AppColors.onSurface,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: AppColors.primarySeed),
            onPressed: _showEditDialog,
            tooltip: '체크리스트 편집',
          ),
          // 저장 버튼 (상시 표시)
          if (checklistState.isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.check, color: AppColors.primarySeed),
              onPressed: checklistState.hasChanges ? _saveChanges : null,
              tooltip: '변경사항 저장',
            ),
        ],
      ),
      body: SafeArea(
        child: _buildBody(checklistState),
      ),
      ),
    );

  }

  /// 상태에 따른 본문 위젯 빌드
  Widget _buildBody(ChecklistState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.textColorError.withValues(alpha: 0.6),
            ),
            const SizedBox(height: Dimens.space16),
            const Text(
              '데이터를 불러오는 중 오류가 발생했습니다',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: Dimens.space8),
            Text(
              state.error!,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Dimens.space24),
            ElevatedButton(
              onPressed: () => ref.read(checklistNotifierProvider.notifier).loadData(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primarySeed,
                foregroundColor: AppColors.white,
              ),
              child: const Text('다시 시도'),
            ),
          ],
        ),
      );
    }

    if (state.templates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.checklist_outlined,
              size: 64,
              color: AppColors.onSurfaceVariant.withValues(alpha: 0.6),
            ),
            const SizedBox(height: Dimens.space16),
            const Text(
              '체크리스트가 없습니다',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: Dimens.space8),
            const Text(
              '우상단 편집 버튼을 눌러 체크리스트를 추가해보세요',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Dimens.space24),
            ElevatedButton.icon(
              onPressed: () => _showEditDialog(),
              icon: const Icon(Icons.add),
              label: const Text('체크리스트 추가'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primarySeed,
                foregroundColor: AppColors.white,
              ),
            ),
          ],
        ),
      );
    }

    // 진행률 계산 (변경사항 포함)
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    int completedCount = 0;
    if (currentWorkspace != null) {
      for (final template in state.templates) {
        if (state.getEffectiveCheckState(template.id!, currentWorkspace.id)) {
          completedCount++;
        }
      }
    }
    final totalCount = state.templates.length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Column(
      children: [
        // 진행률 표시
        Container(
          margin: const EdgeInsets.all(Dimens.space16),
          padding: const EdgeInsets.all(Dimens.space16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '진행 상황',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.onSurface,
                    ),
                  ),
                  Text(
                    '$completedCount/$totalCount',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: Dimens.space12),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: AppColors.dividerColor,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        ),

        // 체크리스트 목록
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: Dimens.space16),
            itemCount: state.templates.length,
            itemBuilder: (context, index) {
              final template = state.templates[index];
              final currentWorkspace = ref.read(currentWorkspaceProvider);

              // 변경사항을 포함한 현재 체크 상태 확인
              final isChecked = currentWorkspace != null
                  ? state.getEffectiveCheckState(template.id!, currentWorkspace.id)
                  : false;

              return Container(
                margin: const EdgeInsets.only(bottom: Dimens.space8),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: Dimens.space16,
                    vertical: Dimens.space2,
                  ),
                  leading: Checkbox(
                    value: isChecked,
                    onChanged: (_) => _toggleChecklistItem(template.id!),
                    activeColor: AppColors.primarySeed,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  title: Text(
                    template.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isChecked
                        ? AppColors.onSurfaceVariant
                        : AppColors.onSurface,
                      decoration: isChecked
                        ? TextDecoration.lineThrough
                        : TextDecoration.none,
                    ),
                  ),

                  onTap: () => _toggleChecklistItem(template.id!),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
