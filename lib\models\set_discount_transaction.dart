import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';
import 'set_discount.dart';

part 'set_discount_transaction.freezed.dart';
part 'set_discount_transaction.g.dart';

/// 세트 할인 거래 정보를 표현하는 데이터 모델 클래스
/// 세트 할인을 개별 상품에 분할하지 않고 거래 단위로 관리
@freezed
abstract class SetDiscountTransaction with _$SetDiscountTransaction {
  const factory SetDiscountTransaction({
    int? id,
    required String batchSaleId, // 어떤 거래에 적용되었는지
    required List<AppliedSetDiscountData> appliedDiscounts, // 적용된 세트 할인들
    required int totalDiscountAmount, // 총 할인 금액
    required int appliedCount, // 실제 세트 할인 적용 횟수
    required DateTime createdAt,
    required int eventId,
  }) = _SetDiscountTransaction;

  factory SetDiscountTransaction.fromJson(Map<String, dynamic> json) => 
      _$SetDiscountTransactionFromJson(json);

  // SQLite 맵에서 직접 생성
  factory SetDiscountTransaction.fromMap(Map<String, dynamic> map) {
    // appliedDiscounts JSON 파싱
    List<AppliedSetDiscountData> appliedDiscounts = [];
    if (map['appliedDiscounts'] != null) {
      final List<dynamic> discountsJson = jsonDecode(map['appliedDiscounts']);
      appliedDiscounts = discountsJson
          .map((json) => AppliedSetDiscountData.fromJson(json))
          .toList();
    }

    return SetDiscountTransaction(
      id: map['id'],
      batchSaleId: map['batchSaleId'] ?? '',
      appliedDiscounts: appliedDiscounts,
      totalDiscountAmount: map['totalDiscountAmount'] ?? 0,
      appliedCount: map['appliedCount'] ?? 0,
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
      eventId: map['eventId'] as int? ?? 1,
    );
  }



  // 현재 타임스탬프로 생성하는 팩토리
  factory SetDiscountTransaction.create({
    int? id,
    required String batchSaleId,
    required List<AppliedSetDiscountData> appliedDiscounts,
    required int totalDiscountAmount,
    required int appliedCount,
    DateTime? createdAt,
    required int eventId,
  }) {
    return SetDiscountTransaction(
      id: id,
      batchSaleId: batchSaleId,
      appliedDiscounts: appliedDiscounts,
      totalDiscountAmount: totalDiscountAmount,
      appliedCount: appliedCount,
      createdAt: createdAt ?? DateTime.now(),
      eventId: eventId,
    );
  }
}

/// SetDiscountTransaction extension for database operations
extension SetDiscountTransactionExtension on SetDiscountTransaction {
  /// SQLite 맵으로 변환
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'batchSaleId': batchSaleId,
      'appliedDiscounts': jsonEncode(appliedDiscounts.map((e) => e.toJson()).toList()),
      'totalDiscountAmount': totalDiscountAmount,
      'appliedCount': appliedCount,
      'createdAt': createdAt.toIso8601String(),
      'eventId': eventId,
    };
  }

  /// Firebase용 JSON 변환 (id 제외)
  Map<String, dynamic> toFirebaseJson() {
    return {
      'batchSaleId': batchSaleId,
      'appliedDiscounts': appliedDiscounts.map((e) => e.toJson()).toList(),
      'totalDiscountAmount': totalDiscountAmount,
      'appliedCount': appliedCount,
      'createdAt': createdAt.toIso8601String(),
      'eventId': eventId,
    };
  }

  /// Firebase JSON에서 생성 (id는 문서 ID로 설정)
  static SetDiscountTransaction fromFirebaseJson(Map<String, dynamic> json, int id) {
    // appliedDiscounts 파싱
    List<AppliedSetDiscountData> appliedDiscounts = [];
    if (json['appliedDiscounts'] != null) {
      final List<dynamic> discountsJson = json['appliedDiscounts'];
      appliedDiscounts = discountsJson
          .map((json) => AppliedSetDiscountData.fromJson(json))
          .toList();
    }

    return SetDiscountTransaction(
      id: id,
      batchSaleId: json['batchSaleId'] ?? '',
      appliedDiscounts: appliedDiscounts,
      totalDiscountAmount: json['totalDiscountAmount'] ?? 0,
      appliedCount: json['appliedCount'] ?? 0,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      eventId: json['eventId'] as int? ?? 1,
    );
  }
}

/// 적용된 세트 할인 데이터 (JSON 직렬화 가능)
@freezed
abstract class AppliedSetDiscountData with _$AppliedSetDiscountData {
  const factory AppliedSetDiscountData({
    required String setDiscountName,
    required int discountAmount,
    required int appliedCount,
    required SetDiscountConditionType conditionType,
  }) = _AppliedSetDiscountData;

  factory AppliedSetDiscountData.fromJson(Map<String, dynamic> json) => 
      _$AppliedSetDiscountDataFromJson(json);

  // AppliedSetDiscount에서 변환
  factory AppliedSetDiscountData.fromAppliedSetDiscount(AppliedSetDiscount applied) {
    return AppliedSetDiscountData(
      setDiscountName: applied.setDiscount.name,
      discountAmount: applied.setDiscount.discountAmount,
      appliedCount: applied.appliedCount,
      conditionType: applied.setDiscount.conditionType,
    );
  }
}

/// SetDiscountService의 AppliedSetDiscount 클래스 (기존 유지)
class AppliedSetDiscount {
  final SetDiscount setDiscount;
  final int appliedCount; // 몇 세트가 적용되었는지

  const AppliedSetDiscount({
    required this.setDiscount,
    required this.appliedCount,
  });

  int get totalDiscountAmount => setDiscount.discountAmount * appliedCount;
}
