import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../providers/seller_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/app_colors.dart';
import '../utils/dialog_theme.dart' as custom_dialog;
import 'modern_dialog_components.dart';

/// 통계 전용 필터 다이얼로그
/// 판매자 + 기간만 포함 (정렬과 거래유형 제외)
class StatisticsFilterDialog extends ConsumerStatefulWidget {
  final String? initialSeller;
  final DateTimeRange? initialDateRange;

  const StatisticsFilterDialog({
    super.key,
    this.initialSeller,
    this.initialDateRange,
  });

  @override
  ConsumerState<StatisticsFilterDialog> createState() => _StatisticsFilterDialogState();
}

class _StatisticsFilterDialogState extends ConsumerState<StatisticsFilterDialog> {
  late String _selectedSeller;
  late DateTimeRange? _selectedDateRange;
  late DateRangePickerController _dateController;

  @override
  void initState() {
    super.initState();
    _selectedSeller = widget.initialSeller ?? '전체 판매자';
    _selectedDateRange = widget.initialDateRange;
    _dateController = DateRangePickerController();
    
    if (_selectedDateRange != null) {
      _dateController.selectedRange = PickerDateRange(
        _selectedDateRange!.start,
        _selectedDateRange!.end,
      );
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sellerAsync = ref.watch(sellerNotifierProvider);
    final allSellersOption = '전체 판매자';
    final sellerNames = sellerAsync.isLoading
        ? <String>[]
        : sellerAsync.hasError
            ? <String>[]
            : sellerAsync.sellers.map((s) => s.name).toList()..sort();
    final sellers = [allSellersOption, ...sellerNames];
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildResponsiveDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 제목
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  size: isTablet ? 20 : 18,
                  color: AppColors.primarySeed,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '통계 필터',
                    style: custom_dialog.DialogTheme.titleStyle.copyWith(
                      fontSize: isTablet ? 20.0 : 18.0,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 내용
            Container(
              constraints: BoxConstraints(
                maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 판매자 선택 섹션
                    _buildSellerSection(sellers, isTablet),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                    // 기간 선택 섹션
                    _buildDateRangeSection(isTablet),
                  ],
                ),
              ),
            ),

            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 버튼
            Row(
              children: [
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '초기화',
                    onPressed: () {
                      setState(() {
                        _selectedSeller = allSellersOption;
                        _selectedDateRange = null;
                        _dateController.selectedRange = null;
                      });
                    },
                    isTablet: isTablet,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '적용',
                    onPressed: () {
                      Navigator.of(context).pop({
                        'seller': _selectedSeller,
                        'dateRange': _selectedDateRange,
                      });
                    },
                    isTablet: isTablet,
                    isPrimary: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 판매자 선택 섹션 (드롭다운)
  Widget _buildSellerSection(List<String> sellers, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '판매자',
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
            fontFamily: 'Pretendard',
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.onSurfaceVariant),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.onSurfaceVariant),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: isTablet ? 16 : 12,
              vertical: isTablet ? 14 : 12,
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          value: _selectedSeller,
          items: sellers.map((seller) => DropdownMenuItem(
            value: seller,
            child: Text(
              seller,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 16 : 14,
                color: AppColors.onSurface,
              ),
            ),
          )).toList(),
          onChanged: (value) {
            setState(() {
              _selectedSeller = value ?? '전체 판매자';
            });
          },
        ),
      ],
    );
  }

  /// 기간 선택 섹션
  Widget _buildDateRangeSection(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                '기간',
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                  fontFamily: 'Pretendard',
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedDateRange = null;
                  _dateController.selectedRange = null;
                });
              },
              child: Text(
                '전체 기간',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  color: AppColors.primarySeed,
                  fontWeight: FontWeight.w500,
                  fontSize: isTablet ? 14 : 12,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 달력 버튼
        ModernDialogComponents.buildPOSSelectionTile(
          context: context,
          title: _selectedDateRange != null
              ? '${_selectedDateRange!.start.month}/${_selectedDateRange!.start.day} - ${_selectedDateRange!.end.month}/${_selectedDateRange!.end.day}'
              : '날짜 선택',
          subtitle: _selectedDateRange != null ? '선택된 기간' : '달력에서 기간을 선택하세요',
          isSelected: _selectedDateRange != null,
          isTablet: isTablet,
          trailing: Icon(
            Icons.calendar_today,
            color: _selectedDateRange != null ? AppColors.primarySeed : AppColors.onSurfaceVariant,
            size: isTablet ? 20 : 18,
          ),
          onTap: () => _showDateRangePicker(),
        ),
      ],
    );
  }

  /// 날짜 범위 선택 다이얼로그 표시
  void _showDateRangePicker() {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    final calendarSize = custom_dialog.DialogTheme.getCalendarDialogSize(context);

    // 행사 날짜 범위 설정 (행사가 없으면 기본값 사용)
    DateTime minDate = currentWorkspace?.startDate ?? DateTime.now().subtract(const Duration(days: 365));
    DateTime maxDate = currentWorkspace?.endDate ?? DateTime.now().add(const Duration(days: 365));

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: AppColors.surface,
          surfaceTintColor: Colors.transparent,
          title: Text(
            '기간 선택',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: SizedBox(
            width: calendarSize.width,
            height: calendarSize.height,
            child: SfDateRangePicker(
              selectionMode: DateRangePickerSelectionMode.range,
              initialSelectedRange: _selectedDateRange != null
                  ? PickerDateRange(_selectedDateRange!.start, _selectedDateRange!.end)
                  : null,
              minDate: minDate,
              maxDate: maxDate,
              backgroundColor: AppColors.surface,
              todayHighlightColor: AppColors.primarySeed,
              selectionColor: AppColors.primarySeed,
              startRangeSelectionColor: AppColors.primarySeed,
              endRangeSelectionColor: AppColors.primarySeed,
              rangeSelectionColor: AppColors.primarySeed.withValues(alpha: 0.3),
              selectionTextStyle: TextStyle(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.w500,
                fontFamily: 'Pretendard',
              ),
              rangeTextStyle: TextStyle(
                color: AppColors.onSurface,
                fontWeight: FontWeight.w400,
                fontFamily: 'Pretendard',
              ),
              headerStyle: DateRangePickerHeaderStyle(
                backgroundColor: AppColors.surface,
                textStyle: TextStyle(
                  color: AppColors.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Pretendard',
                ),
              ),
              monthViewSettings: DateRangePickerMonthViewSettings(
                firstDayOfWeek: 1, // 월요일부터 시작
                dayFormat: 'EEE',
                viewHeaderStyle: DateRangePickerViewHeaderStyle(
                  backgroundColor: AppColors.surface,
                  textStyle: TextStyle(
                    color: AppColors.onSurfaceVariant,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Pretendard',
                  ),
                ),
              ),
              monthCellStyle: DateRangePickerMonthCellStyle(
                textStyle: TextStyle(
                  fontFamily: 'Pretendard',
                  color: AppColors.onSurface,
                ),
              ),
              onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                if (args.value is PickerDateRange) {
                  final range = args.value as PickerDateRange;
                  if (range.startDate != null) {
                    // endDate가 null이면 startDate와 같은 날짜로 설정 (단일 날짜 선택)
                    final endDate = range.endDate ?? range.startDate!;
                    setState(() {
                      _selectedDateRange = DateTimeRange(start: range.startDate!, end: endDate);
                      _dateController.selectedRange = PickerDateRange(range.startDate!, endDate);
                    });
                  }
                }
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '취소',
                style: TextStyle(color: AppColors.onSurfaceVariant),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primarySeed,
                foregroundColor: AppColors.onPrimary,
              ),
              child: const Text('확인'),
            ),
          ],
        );
      },
    );
  }
}
