class PaymentMethod {
  final String id; // e.g., uuid or name-based id
  final String name; // e.g., '현금', '계좌이체'
  final int order; // display order
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  PaymentMethod({
    required this.id,
    required this.name,
    this.order = 0,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
  });

  PaymentMethod copyWith({
    String? id,
    String? name,
    int? order,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      name: name ?? this.name,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'order': order,
        'isActive': isActive,
        'createdAt': createdAt?.millisecondsSinceEpoch,
        'updatedAt': updatedAt?.millisecondsSinceEpoch,
      };

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => PaymentMethod(
        id: json['id']?.toString() ?? '',
        name: json['name']?.toString() ?? '',
        order: (json['order'] as num?)?.toInt() ?? 0,
        // 기존 데이터 호환성: isActive 필드가 없으면 기본값 true (기존 결제수단은 모두 활성화)
        isActive: json.containsKey('isActive')
            ? (json['isActive'] is bool)
                ? json['isActive'] as bool
                : ((json['isActive'] as num?)?.toInt() ?? 1) == 1
            : true, // 기존 데이터 호환성을 위한 기본값
        createdAt: json['createdAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((json['createdAt'] as num).toInt())
            : null,
        updatedAt: json['updatedAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((json['updatedAt'] as num).toInt())
            : null,
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'methodOrder': order,
        'isActive': isActive ? 1 : 0,
        'createdAt': createdAt?.millisecondsSinceEpoch,
        'updatedAt': updatedAt?.millisecondsSinceEpoch,
      };

  factory PaymentMethod.fromMap(Map<String, dynamic> map) => PaymentMethod(
        id: map['id']?.toString() ?? '',
        name: map['name']?.toString() ?? '',
        order: (map['methodOrder'] as num?)?.toInt() ?? 0,
        isActive: (map['isActive'] as num?)?.toInt() == 1,
        createdAt: map['createdAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((map['createdAt'] as num).toInt())
            : null,
        updatedAt: map['updatedAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((map['updatedAt'] as num).toInt())
            : null,
      );
}

