import 'package:freezed_annotation/freezed_annotation.dart';

part 'nickname.freezed.dart';
part 'nickname.g.dart';

@freezed
abstract class Nickname with _$Nickname {
  const factory Nickname({
    required String name,
    required int sellerId,
    String? profileImagePath,
  }) = _Nickname;

  factory Nickname.fromJson(Map<String, dynamic> json) => _$<PERSON>name<PERSON>(json);
}

extension NicknameMapper on Nickname {
  Map<String, dynamic> toMap() => {
    'name': name,
    'sellerId': sellerId,
    if (profileImagePath != null) 'profileImagePath': profileImagePath,
  };
  static Nickname fromMap(Map<String, dynamic> map) => Nickname(
    name: map['name'] as String,
    sellerId: map['sellerId'] as int,
    profileImagePath: map['profileImagePath'] as String?,
  );
} 