import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/settings_provider.dart';


import '../../utils/logger_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../utils/app_colors.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with RestorationMixin {
  @override
  String? get restorationId => 'settings_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   ref.read(settingsNotifierProvider.notifier).loadSettings();
    // });
  }

  @override
  Widget build(BuildContext context) {
    // 닉네임 관련 코드 완전 제거
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '설정',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: ListView(
          restorationId: 'settings_list_scroll',
          cacheExtent: 500,
          children: [
            // 닉네임 ListTile 완전 삭제
            // RepaintBoundary(child: ListTile(title: Text('설정 항목1'))),
            // 요일 설정 등 나머지 항목은 그대로 유지
          _buildSettingItem(
            title: '선입금 설정',
            onTap: () => _showPrepaymentSettings(),
          ),
          // [삭제] 판매자 관리 메뉴
          // _buildSettingItem(
          //   title: '판매자 관리',
          //   onTap: () => _navigateToSellerManagement(),
          // ),



          // [삭제] 선입금 상품 관리
          // _buildSettingItem(
          //   title: '선입금 상품 관리',
          //   onTap: () {
          //     Navigator.of(context).push(
          //       MaterialPageRoute(
          //         builder: (context) => const PrepaymentVirtualProductManagementScreen(),
          //       ),
          //     );
          //   },
          // ),
          // [삭제] 선입금-상품 연동 관리
          // _buildSettingItem(
          //   title: '선입금-상품 연동 관리',
          //   onTap: () {
          //     Navigator.of(context).push(
          //       MaterialPageRoute(
          //         builder: (context) => const PrepaymentProductLinkScreen(),
          //       ),
          //     );
          //   },
          // ),

          // 앱 버전 정보 (하단에 표시)
          const SizedBox(height: 40),
          Center(
            child: Text(
              'parabara v1.0.0',
              style: TextStyle(
                fontSize: 15,
                color: Colors.grey,
                fontFamily: 'Pretendard',
              ),
            ),
          ),
          const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 설정 아이템 위젯
  Widget _buildSettingItem({
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Material(
        child: InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                fontFamily: 'Pretendard',
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 요일 설정 다이얼로그
  // Future<void> _showDayOfWeekSettings() async {
  //   await showDialog(
  //     context: context,
  //     builder: (context) => Consumer(
  //       builder: (context, ref, child) {
  //         return _DayOfWeekSettingsDialog(
  //           enabledDays: ref.watch(enabledDaysOfWeekProvider),
  //           onDaysChanged: (days) {
  //             ref.read(settingsNotifierProvider.notifier).setEnabledDaysOfWeek(days);
  //           },
  //         );
  //       },
  //     ),
  //   );
  // }

  /// 선입금 설정 다이얼로그
  Future<void> _showPrepaymentSettings() async {
    await showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          return ref.watch(settingsNotifierProvider).when(
            data: (state) => _PrepaymentSettingsDialog(
              collectDayOfWeekFromExcel: state.collectDayOfWeekFromExcel,
              excelDayOfWeekColumnIndex: state.excelDayOfWeekColumnIndex,
              linkPrepaymentToInventory: state.linkPrepaymentToInventory,
              onCollectDayOfWeekChanged: (value) async {
                LoggerUtils.logInfo('요일 수집 설정 변경: $value', tag: 'SettingsScreen');
                await ref
                    .read(settingsNotifierProvider.notifier)
                    .setCollectDayOfWeekFromExcel(value);
                LoggerUtils.logInfo('요일 수집 설정 변경 완료', tag: 'SettingsScreen');
              },
              onColumnIndexChanged: (value) async {
                LoggerUtils.logInfo('요일 열 인덱스 변경: $value', tag: 'SettingsScreen');
                await ref
                    .read(settingsNotifierProvider.notifier)
                    .setExcelDayOfWeekColumnIndex(value);
                LoggerUtils.logInfo('요일 열 인덱스 변경 완료', tag: 'SettingsScreen');
              },
              onLinkInventoryChanged: (value) async {
                LoggerUtils.logInfo('재고 연동 설정 변경: $value', tag: 'SettingsScreen');
                await ref
                    .read(settingsNotifierProvider.notifier)
                    .setLinkPrepaymentToInventory(value);
                LoggerUtils.logInfo('재고 연동 설정 변경 완료', tag: 'SettingsScreen');
              },
            ),
            loading: () => const AlertDialog(
              title: Text('선입금 설정'),
              content: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => AlertDialog(
              title: const Text('선입금 설정'),
              content: Text('설정을 불러오는 중 오류가 발생했습니다: ${error.toString()}'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('확인'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }




}

/// 선입금 설정 다이얼로그
class _PrepaymentSettingsDialog extends StatefulWidget {
  final bool collectDayOfWeekFromExcel;
  final int excelDayOfWeekColumnIndex;
  final bool linkPrepaymentToInventory;
  final ValueChanged<bool> onCollectDayOfWeekChanged;
  final ValueChanged<int> onColumnIndexChanged;
  final ValueChanged<bool> onLinkInventoryChanged;

  const _PrepaymentSettingsDialog({
    required this.collectDayOfWeekFromExcel,
    required this.excelDayOfWeekColumnIndex,
    required this.linkPrepaymentToInventory,
    required this.onCollectDayOfWeekChanged,
    required this.onColumnIndexChanged,
    required this.onLinkInventoryChanged,
  });

  @override
  State<_PrepaymentSettingsDialog> createState() =>
      _PrepaymentSettingsDialogState();
}

class _PrepaymentSettingsDialogState extends State<_PrepaymentSettingsDialog> {
  late TextEditingController _dayOfWeekController;

  /// 인덱스를 엑셀 열 문자로 변환합니다 (0 -> A, 1 -> B, ...)
  String _indexToColumnLetter(int index) {
    if (index < 0) return '';
    String result = '';
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result;
      index = (index ~/ 26) - 1;
    }
    return result;
  }

  @override
  void initState() {
    super.initState();
    _dayOfWeekController = TextEditingController(
      text: widget.excelDayOfWeekColumnIndex >= 0
          ? _indexToColumnLetter(widget.excelDayOfWeekColumnIndex)
          : '',
    );
  }

  @override
  void dispose() {
    _dayOfWeekController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 아이콘과 제목을 한 줄로 배치
            Row(
              children: [
                custom_dialog.DialogTheme.buildCompactIconContainer(
                  icon: Icons.account_balance_wallet_rounded,
                  color: AppColors.onboardingAccent,
                  isTablet: isTablet,
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                Expanded(
                  child: Text(
                    '선입금 설정',
                    style: custom_dialog.DialogTheme.titleStyle.copyWith(
                      fontSize: isTablet ? 20.0 : 18.0,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 안내 메시지 (더 컴팩트)
            Container(
              padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.onboardingPrimary.withValues(alpha: 0.1),
                    AppColors.onboardingPrimaryLight.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: AppColors.onboardingPrimary,
                    size: isTablet ? 20.0 : 16.0,
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                  Expanded(
                    child: Text(
                      '엑셀에서 요일 수집 설정은 엑셀 일괄 등록 화면 우측 상단에서 변경할 수 있습니다.',
                      style: TextStyle(
                        color: AppColors.onboardingTextSecondary,
                        fontSize: isTablet ? 14.0 : 12.0,
                        height: 1.3,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 재고 연동 설정 (더 컴팩트)
            Container(
              padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.surfaceVariant,
                    AppColors.secondary.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isTablet ? 8.0 : 6.0),
                    decoration: BoxDecoration(
                      gradient: widget.linkPrepaymentToInventory
                        ? LinearGradient(
                            colors: [
                              AppColors.success,
                              AppColors.successLight,
                            ],
                          )
                        : LinearGradient(
                            colors: [
                              AppColors.secondary,
                              AppColors.secondaryLight,
                            ],
                          ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.link_rounded,
                      color: widget.linkPrepaymentToInventory
                        ? AppColors.onboardingTextOnPrimary
                        : AppColors.onboardingTextSecondary,
                      size: isTablet ? 20.0 : 16.0,
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '재고 연동',
                          style: TextStyle(
                            fontSize: isTablet ? 16.0 : 14.0,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onboardingTextPrimary,
                          ),
                        ),
                        Text(
                          '선입금 수령 시 재고 자동 차감',
                          style: TextStyle(
                            fontSize: isTablet ? 14.0 : 12.0,
                            color: AppColors.onboardingTextSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: widget.linkPrepaymentToInventory,
                    onChanged: (value) => widget.onLinkInventoryChanged(value),
                    activeColor: AppColors.success,
                    activeTrackColor: AppColors.success.withValues(alpha: 0.3),
                    inactiveThumbColor: AppColors.secondary,
                    inactiveTrackColor: AppColors.secondary.withValues(alpha: 0.3),
                  ),
                ],
              ),
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),

            // 버튼들 (더 컴팩트)
            Row(
              children: [
                // 취소 버튼
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.secondary),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? 14.0 : 12.0,
                          ),
                          child: Text(
                            '취소',
                            style: TextStyle(
                              color: AppColors.onboardingTextSecondary,
                              fontSize: isTablet ? 16.0 : 14.0,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),

                // 확인 버튼
                Expanded(
                  child: custom_dialog.DialogTheme.buildGradientButton(
                    decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                    isCompact: true,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '확인',
                      style: TextStyle(
                        color: AppColors.onboardingTextOnPrimary,
                        fontSize: isTablet ? 16.0 : 14.0,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}



