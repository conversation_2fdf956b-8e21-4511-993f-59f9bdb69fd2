import 'package:flutter/material.dart';
import 'dart:math';

/// 앱에서 사용하는 크기 관련 상수들을 정의합니다.
class Dimens {
  // Spacing System (8px base grid)
  static const double space2 = 2.0;
  static const double space4 = 4.0;
  static const double space6 = 6.0;
  static const double space8 = 8.0;
  static const double space10 = 10.0;
  static const double space12 = 12.0;
  static const double space16 = 16.0;
  static const double space20 = 20.0;
  static const double space24 = 24.0;
  static const double space32 = 32.0;
  static const double space40 = 40.0;
  static const double space48 = 48.0;
  static const double space56 = 56.0;
  static const double space64 = 64.0;

  // Radius System (Modern, Expressive)
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 24.0;
  static const double radiusFull = 999.0; // Fully rounded

  // Elevation System (Material 3)
  static const double elevation0 = 0.0;
  static const double elevation1 = 1.0;
  static const double elevation2 = 2.0;
  static const double elevation3 = 3.0;
  static const double elevation4 = 4.0;
  static const double elevation6 = 6.0;
  static const double elevation8 = 8.0;
  static const double elevation12 = 12.0;
  static const double elevation16 = 16.0;
  static const double elevation24 = 24.0;

  // Component Sizes
  static const double iconSizeS = 16.0;
  static const double iconSizeM = 20.0;
  static const double iconSizeL = 24.0;
  static const double iconSizeXL = 32.0;
  static const double iconSizeXXL = 48.0;

  // Button Heights (Material 3)
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;
  static const double appBarScrolledElevation = 2.0;

  // Cards (2025 Design)
  static const double cardElevationModern = 1.0;
  static const double cardRadiusModern = radiusM;
  static const double cardPadding = space16;
  static const double cardMargin = space8;

  // Lists
  static const double listItemHeight = 56.0;
  static const double listItemPadding = space16;
  static const double listItemSpacing = space8;

  // Grid
  static const double gridSpacing = space4;
  static const double gridPadding = space4;

  // Input Fields
  static const double inputHeight = 48.0;
  static const double inputRadius = radiusM;
  static const double inputPadding = space16;

  // Snackbar/Toast (사용자 요구사항 준수)
  static const double toastBottomMargin = 80.0; // 화면 하단에서 80px 위
  static const double toastHorizontalMargin = 16.0; // 좌우 패딩 16px
  static const double toastRadius = radiusL;
  static const double toastPadding = space16;

  // FAB
  static const double fabSize = 56.0;
  static const double fabSizeSmall = 40.0;
  static const double fabSizeLarge = 96.0;
  static const double fabRadius = radiusL;

  // Dividers
  static const double dividerThickness = 1.0;
  static const double dividerIndent = space16;

  // Images & Avatars
  static const double avatarSizeS = 24.0;
  static const double avatarSizeM = 32.0;
  static const double avatarSizeL = 40.0;
  static const double avatarSizeXL = 56.0;
  static const double avatarSizeXXL = 72.0;

  // Responsive Breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 840.0;
  static const double desktopBreakpoint = 1200.0;

  // Animation Durations (ms)
  static const int animationDurationFast = 150;
  static const int animationDurationNormal = 300;
  static const int animationDurationSlow = 500;

  // Z-Index / Layers
  static const double zIndexBase = 0.0;
  static const double zIndexRaised = 1.0;
  static const double zIndexFloating = 2.0;
  static const double zIndexModal = 3.0;
  static const double zIndexTooltip = 4.0;

  // 반응형 헬퍼 메서드들
  static bool isMobile(double width) => width < mobileBreakpoint;
  static bool isTabletByWidth(double width) =>
      width >= mobileBreakpoint && width < desktopBreakpoint;
  static bool isDesktop(double width) => width >= desktopBreakpoint;

  /// 가장 안정적인 타블렛 감지 (물리적 픽셀 기준)
  static bool isTablet(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    
    // 물리적 픽셀 계산 (더 안정적)
    final physicalWidth = size.width * devicePixelRatio;
    final physicalHeight = size.height * devicePixelRatio;
    final physicalDiagonal = sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    
    // 인치 기준으로 판단 (1인치 = 96픽셀, 7인치 이상을 타블렛으로)
    final diagonalInches = physicalDiagonal / 96;
    
    // 추가로 최소 크기 체크 (안전장치)
    final minPhysicalSide = min(physicalWidth, physicalHeight);
    final minInches = minPhysicalSide / 96;
    
    return diagonalInches >= 7.0 || minInches >= 4.0;
  }

  /// 화면 방향을 고려한 타블렛 감지
  static bool isTabletByOrientation(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final orientation = mediaQuery.orientation;
    final tabletDetected = isTablet(context);
    
    if (!tabletDetected) return false;
    
    // 타블렛이 확인된 경우, 방향에 따라 세밀 조정
    if (orientation == Orientation.portrait) {
      // 세로모드: 더 엄격한 기준
      final size = mediaQuery.size;
      final devicePixelRatio = mediaQuery.devicePixelRatio;
      final physicalWidth = size.width * devicePixelRatio;
      final physicalHeight = size.height * devicePixelRatio;
      final minPhysicalSide = min(physicalWidth, physicalHeight);
      return minPhysicalSide / 96 >= 4.5; // 4.5인치 이상
    } else {
      // 가로모드: 더 관대한 기준
      return true;
    }
  }

  /// 디버깅용 디바이스 정보 출력
  static Map<String, dynamic> getDeviceInfo(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final orientation = mediaQuery.orientation;
    
    final physicalWidth = size.width * devicePixelRatio;
    final physicalHeight = size.height * devicePixelRatio;
    final physicalDiagonal = sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    final diagonalInches = physicalDiagonal / 96;
    final minPhysicalSide = min(physicalWidth, physicalHeight);
    final minInches = minPhysicalSide / 96;
    
    return {
      'logicalSize': '${size.width.toStringAsFixed(1)} x ${size.height.toStringAsFixed(1)}',
      'physicalSize': '${physicalWidth.toStringAsFixed(1)} x ${physicalHeight.toStringAsFixed(1)}',
      'devicePixelRatio': devicePixelRatio.toStringAsFixed(2),
      'diagonalInches': diagonalInches.toStringAsFixed(1),
      'minInches': minInches.toStringAsFixed(1),
      'orientation': orientation.toString(),
      'isTablet': isTablet(context),
      'isTabletByOrientation': isTabletByOrientation(context),
    };
  }

  // 동적 패딩 계산 (화면 크기에 따라)
  static double getResponsivePadding(double screenWidth) {
    if (screenWidth < mobileBreakpoint) return space16;
    if (screenWidth < tabletBreakpoint) return space24;
    return space32;
  }

  // 동적 마진 계산
  static double getResponsiveMargin(double screenWidth) {
    if (screenWidth < mobileBreakpoint) return space8;
    if (screenWidth < tabletBreakpoint) return space12;
    return space16;
  }

  // 동적 그리드 컬럼 수 계산
  static int getGridColumns(double screenWidth) {
    if (screenWidth < mobileBreakpoint) return 2;
    if (screenWidth < tabletBreakpoint) return 3;
    return 4;
  }

  // 카드 크기 계산 (그리드용)
  static double getCardWidth(double screenWidth, int columns) {
    final availableWidth =
        screenWidth - (getResponsivePadding(screenWidth) * 2);
    final spacingTotal = gridSpacing * (columns - 1);
    return (availableWidth - spacingTotal) / columns;
  }

  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
}
