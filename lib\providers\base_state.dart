import 'package:equatable/equatable.dart';

/// 모든 상태 클래스의 기본 클래스입니다.
/// - 로딩/에러/취소 상태 등 공통 상태 관리
abstract class BaseState extends Equatable {
  /// 데이터 로딩 중 여부
  final bool isLoading;

  /// 에러 메시지 (사용자에게 표시)
  final String? errorMessage;

  /// 에러 코드 (디버깅 및 로깅용)
  final String? errorCode;

  /// 에러 심각도 (처리 우선순위 결정)
  final String? errorSeverity;

  /// 상세 에러 정보 (추가 컨텍스트)
  final Map<String, String>? errorDetails;

  /// 작업 취소 여부 (UI 업데이트 제어)
  final bool isCancelled;

  const BaseState({
    this.isLoading = false,
    this.errorMessage,
    this.errorCode,
    this.errorSeverity,
    this.errorDetails,
    this.isCancelled = false,
  });

  /// 기본 필드를 업데이트하는 copyWith 메서드
  BaseState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  });

  /// 에러 상태 여부 확인
  bool get hasError => errorMessage != null || errorCode != null;

  /// 성공 상태 여부 확인
  bool get isSuccess => !isLoading && !hasError && !isCancelled;

  /// 에러 정보를 포함한 상태 요약
  String get statusSummary {
    if (isCancelled) return 'CANCELLED';
    if (isLoading) return 'LOADING';
    if (hasError) return 'ERROR: $errorMessage';
    return 'SUCCESS';
  }

  @override
  List<Object?> get props => [
    isLoading,
    errorMessage,
    errorCode,
    errorSeverity,
    errorDetails,
    isCancelled,
  ];

  @override
  bool? get stringify => true;

  /// 초기 상태를 반환하는 팩토리 메서드
  static T initialState<T extends BaseState>() {
    throw UnimplementedError('initialState() must be implemented in subclasses');
  }
} 