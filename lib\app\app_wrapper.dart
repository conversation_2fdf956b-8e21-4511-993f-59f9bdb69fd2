import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';
import '../providers/nickname_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/product_provider.dart';
import '../providers/prepayment_provider.dart';
import '../providers/seller_provider.dart';
import '../providers/data_sync_provider.dart';
import '../services/differential_sync_service.dart';
import '../services/realtime_sync_service_main.dart';
import '../services/database_service.dart';
import '../utils/device_utils.dart';
import '../utils/app_colors.dart';
import '../screens/onboarding/event_workspace_onboarding_screen.dart';
import '../screens/records_and_statistics/records_and_statistics_screen.dart';
import '../screens/home/<USER>';
import '../screens/sale/sale_screen.dart';
import '../screens/settings/my_page_screen.dart';
import '../widgets/confirmation_dialog.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';

class AppWrapper extends ConsumerStatefulWidget {
  final Widget child;
  const AppWrapper({super.key, required this.child});
  @override
  ConsumerState<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends ConsumerState<AppWrapper> with WidgetsBindingObserver {
  static const String _tag = 'AppWrapper';
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAppDataInBackground();
  }

  void _initializeAppDataInBackground() async {
    try {
      LoggerUtils.logInfo('[Init] 기기 타입 감지 시작', tag: _tag);
      await _detectAndSetDeviceType();
      LoggerUtils.logInfo('[Init] 설정 로드 시작', tag: _tag);
      await ref.read(settingsNotifierProvider.notifier).loadSettings();
      try {
        LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 초기화 확인', tag: _tag);
        final realtimeSyncService = RealtimeSyncService();
        if (!realtimeSyncService.isInitialized.value) {
          await realtimeSyncService.initialize();
          LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 초기화 완료', tag: _tag);
        } else {
          LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 이미 초기화됨', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logError('[Init] 실시간 동기화 초기화 실패', tag: _tag, error: e);
      }
      _loadDataInBackground();
      LoggerUtils.logInfo('[Init] 초기화 시퀀스 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('[Init] 백그라운드 초기화 실패', tag: _tag, error: e);
    }
  }

  void _loadDataInBackground() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          await _performDataSync();
        }
        final workspaceState = ref.read(unifiedWorkspaceProvider);
        if (workspaceState.hasCurrentWorkspace) {
          await _loadWorkspaceData();
        }
      } catch (e) {
        LoggerUtils.logError('백그라운드 데이터 로딩 실패', tag: _tag, error: e);
      }
    });
  }

  Future<void> _performDataSync() async {
    try {
      LoggerUtils.logInfo('[Sync] 동기화 시퀀스 시작', tag: _tag);
      final prefs = await SharedPreferences.getInstance();
      final syncCompleted = prefs.getBool('sync_completed_recently') ?? false;
      if (syncCompleted) {
        LoggerUtils.logInfo('[Sync] 최근 동기화 완료 플래그 감지 -> 스킵', tag: _tag);
        await prefs.remove('sync_completed_recently');
        return;
      }
      final isNewDevice = await _detectNewDevice();
      if (isNewDevice) {
        LoggerUtils.logInfo('[Sync] 신규 디바이스 감지 -> 전체 다운로드 수행', tag: _tag);
        await _performFullDownloadForNewDevice();
        return;
      }
      LoggerUtils.logInfo('[Sync][1] 이벤트 목록 동기화 시작', tag: _tag);
      final databaseService = ref.read(databaseServiceProvider);
      final diffSyncService = DifferentialSyncService(databaseService);
      await diffSyncService.syncEventsList();
      final workspaceState = ref.read(unifiedWorkspaceProvider);
      if (!workspaceState.hasCurrentWorkspace) {
        LoggerUtils.logInfo('[Sync] 현재 워크스페이스 없음 -> 종료', tag: _tag);
        return;
      }
      LoggerUtils.logInfo('[Sync][2] 현재 워크스페이스 데이터 동기화 시작 (ID: ${workspaceState.currentWorkspace!.id})', tag: _tag);
      await diffSyncService.syncCurrentEventData(workspaceState.currentWorkspace!.id);
      LoggerUtils.logInfo('[Sync] 동기화 시퀀스 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('[Sync] 동기화 실패', tag: _tag, error: e);
    }
  }

  Future<void> _loadWorkspaceData() async {
    try {
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);
      productNotifier.clearError();
      prepaymentNotifier.clearError();
      await productNotifier.loadProducts();
      await prepaymentNotifier.loadPrepayments(showLoading: false);
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
    } catch (e) {
      LoggerUtils.logError('워크스페이스 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  Future<void> _detectAndSetDeviceType() async {
    try {
      if (!mounted) return;
      final isTablet = DeviceUtils.isTablet(context);
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(isTablet);
    } catch (e, st) {
      LoggerUtils.logError('기기 타입 감지 실패', tag: _tag, error: e, stackTrace: st);
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(false);
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<bool> _detectNewDevice() async {
    try {
      final workspaceState = ref.read(unifiedWorkspaceProvider);
      final hasWorkspaces = workspaceState.workspaces.isNotEmpty;
      if (!hasWorkspaces) return true;
      final dataSyncService = ref.read(dataSyncServiceProvider);
      final hasServerData = await dataSyncService.hasServerData();
      if (hasServerData && workspaceState.workspaces.length <= 1) return true;
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _performFullDownloadForNewDevice() async {
    try {
      final dataSyncService = ref.read(dataSyncServiceProvider);
      await dataSyncService.performBidirectionalSync();
      await ref.read(unifiedWorkspaceProvider.notifier).refresh();
    } catch (e) {
      LoggerUtils.logError('전체 다운로드 실패', tag: _tag, error: e);
    }
  }

  void _onTabTapped(int index) {
    if (_currentTabIndex != index) {
      setState(() => _currentTabIndex = index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          final shouldExit = await ConfirmationDialog.show(
            context: context,
            title: '앱 종료',
            message: '앱을 종료하시겠습니까?',
            confirmLabel: '예',
            cancelLabel: '아니오',
          );
          if (shouldExit == true && context.mounted) {
            SystemNavigator.pop();
          }
        },
        child: Consumer(
          builder: (context, ref, _) {
            final nickname = ref.watch(nicknameProvider);
            if (nickname == null) {
              return Center(
                child: Text('닉네임 필요'),
              );
            }
            return _buildWorkspaceCheck();
          },
        ),
      ),
    );
  }

  Widget _buildWorkspaceCheck() {
    final workspaceState = ref.watch(unifiedWorkspaceProvider);
    if (!workspaceState.hasWorkspaces) {
      return EventWorkspaceOnboardingScreen(onWorkspaceCreated: () => setState(() {}));
    }
    if (!workspaceState.hasCurrentWorkspace) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    return _buildMainScreenWithBottomTabs();
  }

  Widget _buildMainScreenWithBottomTabs() {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: _buildCurrentTabContent(),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildFloatingPOSButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildCurrentTabContent() {
    return IndexedStack(
      index: _currentTabIndex,
      children: [
        _buildHomeTab(),
        widget.child,
        Container(),
        const RecordsAndStatisticsScreen(),
        _buildMyTab(),
      ],
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomAppBar(
      shape: const CircularNotchedRectangle(),
      notchMargin: 8,
      child: SizedBox(
        height: 60,
        child: Row(
          children: [
            Expanded(child: _buildNavItem(0, LucideIcons.home, '홈')),
            Expanded(child: _buildNavItem(1, LucideIcons.creditCard, '선입금')),
            Expanded(child: _buildNavItem(2, LucideIcons.shoppingCart, 'POS')),
            Expanded(child: _buildNavItem(3, LucideIcons.fileBarChart, '기록&통계')),
            Expanded(child: _buildNavItem(4, LucideIcons.user, 'MY')),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final selected = _currentTabIndex == index;
    final color = selected ? AppColors.primarySeed : const Color(0xFF9E9E9E);
    return InkWell(
      onTap: () => _onTabTapped(index),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(label, style: TextStyle(color: color, fontSize: 12, fontWeight: selected ? FontWeight.w600 : FontWeight.w400)),
        ],
      ),
    );
  }

  Widget _buildFloatingPOSButton() {
    return FloatingActionButton(
      onPressed: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => const SaleScreen())),
      child: const Icon(LucideIcons.shoppingCart),
    );
  }

  Widget _buildHomeTab() {
    return Scaffold(
      appBar: AppBar(title: const Text('리더보드'), centerTitle: true),
      body: const HomeDashboardScreen(),
    );
  }

  Widget _buildMyTab() => const MyPageScreen();
}
