import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'logger_utils.dart';

/// 메모리 사용량 정보
class MemoryUsage {
  final int totalMemory;
  final int usedMemory;
  final int availableMemory;
  final double usagePercentage;
  final DateTime timestamp;

  MemoryUsage({
    required this.totalMemory,
    required this.usedMemory,
    required this.availableMemory,
    required this.usagePercentage,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// 메가바이트 단위로 변환
  double get totalMemoryMB => totalMemory / (1024 * 1024);
  double get usedMemoryMB => usedMemory / (1024 * 1024);
  double get availableMemoryMB => availableMemory / (1024 * 1024);

  /// 메모리 압박 수준
  MemoryPressureLevel get pressureLevel {
    if (usagePercentage >= 90) return MemoryPressureLevel.critical;
    if (usagePercentage >= 75) return MemoryPressureLevel.high;
    if (usagePercentage >= 50) return MemoryPressureLevel.medium;
    return MemoryPressureLevel.low;
  }

  /// 복사 생성자
  MemoryUsage copyWith({
    int? totalMemory,
    int? usedMemory,
    int? availableMemory,
    double? usagePercentage,
    DateTime? timestamp,
  }) {
    return MemoryUsage(
      totalMemory: totalMemory ?? this.totalMemory,
      usedMemory: usedMemory ?? this.usedMemory,
      availableMemory: availableMemory ?? this.availableMemory,
      usagePercentage: usagePercentage ?? this.usagePercentage,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'MemoryUsage(total: ${totalMemoryMB.toStringAsFixed(1)}MB, '
           'used: ${usedMemoryMB.toStringAsFixed(1)}MB, '
           'available: ${availableMemoryMB.toStringAsFixed(1)}MB, '
           'usage: ${usagePercentage.toStringAsFixed(1)}%)';
  }
}

/// 메모리 압박 수준
enum MemoryPressureLevel {
  low,
  medium,
  high,
  critical,
}

/// 메모리 최적화 전략
enum MemoryOptimizationStrategy {
  none,
  aggressive,
  conservative,
  adaptive,
}

/// 메모리 관리자 설정
class MemoryManagerConfig {
  final Duration monitoringInterval;
  final double criticalThreshold;
  final double highThreshold;
  final double mediumThreshold;
  final bool enableAutoOptimization;
  final MemoryOptimizationStrategy defaultStrategy;
  final int maxCacheSize;
  final Duration cacheExpirationTime;
  final bool enableMemoryLeakDetection;
  final Duration leakDetectionInterval;

  const MemoryManagerConfig({
    this.monitoringInterval = const Duration(seconds: 30),
    this.criticalThreshold = 90.0,
    this.highThreshold = 75.0,
    this.mediumThreshold = 50.0,
    this.enableAutoOptimization = true,
    this.defaultStrategy = MemoryOptimizationStrategy.adaptive,
    this.maxCacheSize = 100,
    this.cacheExpirationTime = const Duration(minutes: 30),
    this.enableMemoryLeakDetection = true,
    this.leakDetectionInterval = const Duration(minutes: 5),
  });

  /// 기본 설정
  factory MemoryManagerConfig.defaultConfig() {
    return const MemoryManagerConfig();
  }

  /// 고성능 설정
  factory MemoryManagerConfig.highPerformance() {
    return const MemoryManagerConfig(
      monitoringInterval: const Duration(seconds: 10),
      criticalThreshold: 85.0,
      highThreshold: 70.0,
      mediumThreshold: 45.0,
      enableAutoOptimization: true,
      defaultStrategy: MemoryOptimizationStrategy.aggressive,
      maxCacheSize: 200,
      cacheExpirationTime: const Duration(minutes: 15),
      enableMemoryLeakDetection: true,
      leakDetectionInterval: const Duration(minutes: 3),
    );
  }

  /// 메모리 효율 설정
  factory MemoryManagerConfig.memoryEfficient() {
    return const MemoryManagerConfig(
      monitoringInterval: const Duration(minutes: 1),
      criticalThreshold: 95.0,
      highThreshold: 80.0,
      mediumThreshold: 60.0,
      enableAutoOptimization: true,
      defaultStrategy: MemoryOptimizationStrategy.conservative,
      maxCacheSize: 50,
      cacheExpirationTime: const Duration(minutes: 60),
      enableMemoryLeakDetection: true,
      leakDetectionInterval: const Duration(minutes: 10),
    );
  }
}

/// 메모리 관리자
class MemoryManager {
  final MemoryManagerConfig config;
  
  Timer? _monitoringTimer;
  Timer? _leakDetectionTimer;
  MemoryUsage? _lastMemoryUsage;
  MemoryPressureLevel _currentPressureLevel = MemoryPressureLevel.low;
  MemoryOptimizationStrategy _currentStrategy;
  
  final List<MemoryUsage> _memoryHistory = [];
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, int> _cacheAccessCount = {};
  
  // 메모리 누수 감지를 위한 참조 추적
  final Map<String, WeakReference> _trackedReferences = {};
  final Map<String, DateTime> _referenceTimestamps = {};
  
  // 콜백 함수들
  void Function(MemoryUsage)? onMemoryUsageChanged;
  void Function(MemoryPressureLevel)? onPressureLevelChanged;
  void Function(MemoryOptimizationStrategy)? onOptimizationStrategyChanged;
  void Function(String)? onMemoryLeakDetected;

  MemoryManager({
    MemoryManagerConfig? config,
  }) : config = config ?? MemoryManagerConfig.defaultConfig(),
       _currentStrategy = config?.defaultStrategy ?? MemoryOptimizationStrategy.adaptive;

  /// 초기화
  Future<void> initialize() async {
    LoggerUtils.logDebug(
      'Initializing memory manager',
      tag: 'MemoryManager',
    );

    await _getCurrentMemoryUsage();
    _startMonitoring();
    
    if (config.enableMemoryLeakDetection) {
      _startLeakDetection();
    }

    LoggerUtils.logDebug(
      'Memory manager initialized',
      tag: 'MemoryManager',
    );
  }

  /// 메모리 사용량 모니터링 시작
  void _startMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(config.monitoringInterval, (_) {
      _monitorMemory();
    });
  }

  /// 메모리 누수 감지 시작
  void _startLeakDetection() {
    _leakDetectionTimer?.cancel();
    _leakDetectionTimer = Timer.periodic(config.leakDetectionInterval, (_) {
      _detectMemoryLeaks();
    });
  }

  /// 메모리 누수 감지
  void _detectMemoryLeaks() {
    final now = DateTime.now();
    final expiredReferences = <String>[];

    for (final entry in _referenceTimestamps.entries) {
      final key = entry.key;
      final timestamp = entry.value;
      final reference = _trackedReferences[key];

      // 30분 이상 된 참조 확인
      if (now.difference(timestamp) > const Duration(minutes: 30)) {
        if (reference?.target == null) {
          // 참조가 해제됨 - 정상
          expiredReferences.add(key);
        } else {
          // 참조가 여전히 존재 - 잠재적 메모리 누수
          LoggerUtils.logWarning(
            'Potential memory leak detected: $key',
            tag: 'MemoryManager',
          );
          onMemoryLeakDetected?.call(key);
        }
      }
    }

    // 만료된 참조 정리
    for (final key in expiredReferences) {
      _trackedReferences.remove(key);
      _referenceTimestamps.remove(key);
    }
  }

  /// 객체 참조 추적 시작
  void trackReference(String key, Object object) {
    _trackedReferences[key] = WeakReference(object);
    _referenceTimestamps[key] = DateTime.now();
    
    LoggerUtils.logDebug(
      'Started tracking reference: $key',
      tag: 'MemoryManager',
    );
  }

  /// 객체 참조 추적 중지
  void untrackReference(String key) {
    _trackedReferences.remove(key);
    _referenceTimestamps.remove(key);
    
    LoggerUtils.logDebug(
      'Stopped tracking reference: $key',
      tag: 'MemoryManager',
    );
  }

  /// 메모리 모니터링
  Future<void> _monitorMemory() async {
    try {
      final usage = await _getCurrentMemoryUsage();
      _lastMemoryUsage = usage;
      _memoryHistory.add(usage);
      
      // 히스토리 크기 제한 (최근 50개)
      if (_memoryHistory.length > 50) {
        _memoryHistory.removeAt(0);
      }
      
      // 메모리 압박 수준 확인
      final newPressureLevel = _getPressureLevel(usage.usagePercentage);
      if (newPressureLevel != _currentPressureLevel) {
        _currentPressureLevel = newPressureLevel;
        onPressureLevelChanged?.call(newPressureLevel);
        
        // 압박 수준에 따른 자동 최적화
        if (config.enableAutoOptimization) {
          await _performAutoOptimizationByPressure(newPressureLevel);
        }
      }
      
      // 캐시 정리
      _cleanupCache();
      
      onMemoryUsageChanged?.call(usage);
      
    } catch (e) {
      LoggerUtils.logError(
        'Memory monitoring failed: $e',
        error: e,
        tag: 'MemoryManager',
      );
    }
  }

  /// 현재 메모리 사용량 가져오기
  Future<MemoryUsage> _getCurrentMemoryUsage() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return await _getNativeMemoryUsage();
      } else {
        return await _getFallbackMemoryUsage();
      }
    } catch (e) {
      LoggerUtils.logWarning(
        'Failed to get memory usage, using fallback: $e',
        tag: 'MemoryManager',
      );
      return await _getFallbackMemoryUsage();
    }
  }

  /// 네이티브 메모리 사용량 가져오기
  Future<MemoryUsage> _getNativeMemoryUsage() async {
    try {
      // ProcessInfo를 사용하여 실제 메모리 사용량 가져오기
      final info = ProcessInfo.currentRss;
      final usedMemory = info;

      // 시스템 총 메모리는 추정값 사용 (실제로는 플랫폼 채널 필요)
      final totalMemory = 4 * 1024 * 1024 * 1024; // 4GB 추정
      final availableMemory = totalMemory - usedMemory;
      final usagePercentage = (usedMemory / totalMemory) * 100;

      return MemoryUsage(
        usedMemory: usedMemory,
        totalMemory: totalMemory,
        availableMemory: availableMemory,
        usagePercentage: usagePercentage,
      );
    } catch (e) {
      // ProcessInfo 사용 실패 시 기본값 반환
      return await _getFallbackMemoryUsage();
    }
  }

  /// 폴백 메모리 사용량 (실제 측정 실패 시)
  Future<MemoryUsage> _getFallbackMemoryUsage() async {
    // 기본값으로 안전한 메모리 사용량 반환
    final totalMemory = 4 * 1024 * 1024 * 1024; // 4GB 기본값
    final usedMemory = (totalMemory * 0.5).round(); // 50% 사용 가정
    final availableMemory = totalMemory - usedMemory;
    final usagePercentage = 50.0;

    return MemoryUsage(
      usedMemory: usedMemory,
      totalMemory: totalMemory,
      availableMemory: availableMemory,
      usagePercentage: usagePercentage,
    );
  }





  /// 자동 최적화 수행 (압박 수준별)
  Future<void> _performAutoOptimizationByPressure(MemoryPressureLevel pressureLevel) async {
    switch (pressureLevel) {
      case MemoryPressureLevel.critical:
        await performOptimization(MemoryOptimizationStrategy.aggressive);
        break;
      case MemoryPressureLevel.high:
        await performOptimization(MemoryOptimizationStrategy.adaptive);
        break;
      case MemoryPressureLevel.medium:
        await performOptimization(MemoryOptimizationStrategy.conservative);
        break;
      case MemoryPressureLevel.low:
        // 낮은 압박에서는 최적화하지 않음
        break;
    }
  }



  /// 공격적 최적화
  Future<void> _performAggressiveOptimization() async {
    LoggerUtils.logInfo(
      'Performing aggressive memory optimization',
      tag: 'MemoryManager',
    );

    // 캐시 완전 정리
    _clearAllCache();
    
    // 메모리 힙 강제 정리
    await _forceGarbageCollection();
    
    // 이미지 캐시 정리
    await _clearImageCache();
  }

  /// 보수적 최적화
  Future<void> _performConservativeOptimization() async {
    LoggerUtils.logInfo(
      'Performing conservative memory optimization',
      tag: 'MemoryManager',
    );

    // 오래된 캐시만 정리
    _cleanupExpiredCache();
    
    // 가비지 컬렉션 요청
    await _requestGarbageCollection();
  }

  /// 적응형 최적화
  Future<void> _performAdaptiveOptimization() async {
    LoggerUtils.logInfo(
      'Performing adaptive memory optimization',
      tag: 'MemoryManager',
    );

    final usage = _lastMemoryUsage;
    if (usage == null) return;

    if (usage.usagePercentage > 80) {
      await _performAggressiveOptimization();
    } else if (usage.usagePercentage > 60) {
      await _performConservativeOptimization();
    }
  }

  /// 메모리 압박 수준 계산
  MemoryPressureLevel _getPressureLevel(double usagePercentage) {
    if (usagePercentage >= config.criticalThreshold) {
      return MemoryPressureLevel.critical;
    } else if (usagePercentage >= config.highThreshold) {
      return MemoryPressureLevel.high;
    } else if (usagePercentage >= config.mediumThreshold) {
      return MemoryPressureLevel.medium;
    } else {
      return MemoryPressureLevel.low;
    }
  }

  /// 캐시에 데이터 저장
  void cacheData(String key, dynamic data) {
    if (_cache.length >= config.maxCacheSize) {
      _removeLeastRecentlyUsed();
    }

    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    _cacheAccessCount[key] = 0; // 접근 횟수 초기화

    LoggerUtils.logDebug(
      'Cached data: $key',
      tag: 'MemoryManager',
    );
  }

  /// 캐시에서 데이터 가져오기
  T? getCachedData<T>(String key) {
    final data = _cache[key];
    if (data != null && data is T) {
      _cacheTimestamps[key] = DateTime.now(); // 접근 시간 업데이트
      _cacheAccessCount[key] = (_cacheAccessCount[key] ?? 0) + 1; // 접근 횟수 증가
      return data;
    }
    return null;
  }

  /// 캐시에서 데이터 제거
  void removeCachedData(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
    _cacheAccessCount.remove(key);

    LoggerUtils.logDebug(
      'Removed cached data: $key',
      tag: 'MemoryManager',
    );
  }

  /// 모든 캐시 정리
  void _clearAllCache() {
    final count = _cache.length;
    _cache.clear();
    _cacheTimestamps.clear();
    _cacheAccessCount.clear();

    LoggerUtils.logInfo(
      'Cleared all cache ($count items)',
      tag: 'MemoryManager',
    );
  }

  /// 만료된 캐시 정리
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > config.cacheExpirationTime) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      removeCachedData(key);
    }

    if (expiredKeys.isNotEmpty) {
      LoggerUtils.logDebug(
        'Cleaned up ${expiredKeys.length} expired cache entries',
        tag: 'MemoryManager',
      );
    }
  }



  /// LRU(Least Recently Used) 알고리즘으로 캐시 항목 제거
  void _removeLeastRecentlyUsed() {
    if (_cacheTimestamps.isEmpty) return;

    String? lruKey;
    DateTime? lruTime;
    int? lruAccessCount;

    for (final entry in _cacheTimestamps.entries) {
      final key = entry.key;
      final timestamp = entry.value;
      final accessCount = _cacheAccessCount[key] ?? 0;

      if (lruTime == null || 
          timestamp.isBefore(lruTime) || 
          (timestamp == lruTime && accessCount < (lruAccessCount ?? 0))) {
        lruTime = timestamp;
        lruKey = key;
        lruAccessCount = accessCount;
      }
    }

    if (lruKey != null) {
      LoggerUtils.logDebug(
        'Removing LRU cache entry: $lruKey (access count: ${lruAccessCount})',
        tag: 'MemoryManager',
      );
      removeCachedData(lruKey);
    }
  }

  /// 캐시 정리
  void _cleanupCache() {
    _cleanupExpiredCache();
    
    // 최대 크기 초과 시 LRU 알고리즘으로 제거
    while (_cache.length > config.maxCacheSize) {
      _removeLeastRecentlyUsed();
    }
  }

  /// 캐시 통계 정보
  Map<String, dynamic> getCacheStats() {
    if (_cache.isEmpty) {
      return {
        'totalEntries': 0,
        'averageAccessCount': 0.0,
        'mostAccessedKey': null,
        'leastAccessedKey': null,
        'cacheHitRate': 0.0,
      };
    }

    final totalAccessCount = _cacheAccessCount.values.fold<int>(0, (sum, count) => sum + count);
    final averageAccessCount = totalAccessCount / _cache.length;

    String? mostAccessedKey;
    int maxAccessCount = 0;
    String? leastAccessedKey;
    int minAccessCount = totalAccessCount;

    for (final entry in _cacheAccessCount.entries) {
      if (entry.value > maxAccessCount) {
        maxAccessCount = entry.value;
        mostAccessedKey = entry.key;
      }
      if (entry.value < minAccessCount) {
        minAccessCount = entry.value;
        leastAccessedKey = entry.key;
      }
    }

    return {
      'totalEntries': _cache.length,
      'averageAccessCount': averageAccessCount,
      'mostAccessedKey': mostAccessedKey,
      'mostAccessedCount': maxAccessCount,
      'leastAccessedKey': leastAccessedKey,
      'leastAccessedCount': minAccessCount,
      'totalAccessCount': totalAccessCount,
    };
  }

  /// 가비지 컬렉션 요청
  Future<void> _requestGarbageCollection() async {
    try {
      // Flutter에서는 직접적인 GC 제어가 제한적이므로
      // 메모리 압박을 줄이는 작업을 수행
      await Future.delayed(const Duration(milliseconds: 100));
      
      LoggerUtils.logDebug(
        'Garbage collection requested',
        tag: 'MemoryManager',
      );
    } catch (e) {
      LoggerUtils.logWarning(
        'Failed to request garbage collection: $e',
        tag: 'MemoryManager',
      );
    }
  }

  /// 강제 가비지 컬렉션
  Future<void> _forceGarbageCollection() async {
    try {
      // 개발 모드에서만 사용 가능한 GC 강제 실행
      if (kDebugMode) {
        developer.log('Forcing garbage collection', name: 'MemoryManager');
      }
      
      await Future.delayed(const Duration(milliseconds: 500));
      
      LoggerUtils.logDebug(
        'Forced garbage collection completed',
        tag: 'MemoryManager',
      );
    } catch (e) {
      LoggerUtils.logWarning(
        'Failed to force garbage collection: $e',
        tag: 'MemoryManager',
      );
    }
  }

  /// 이미지 캐시 정리
  Future<void> _clearImageCache() async {
    try {
      // Flutter의 이미지 캐시 정리
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      LoggerUtils.logDebug(
        'Image cache cleared',
        tag: 'MemoryManager',
      );
    } catch (e) {
      LoggerUtils.logWarning(
        'Failed to clear image cache: $e',
        tag: 'MemoryManager',
      );
    }
  }

  /// 메모리 사용량 히스토리 가져오기
  List<MemoryUsage> getMemoryHistory() {
    return List.unmodifiable(_memoryHistory);
  }

  /// 현재 메모리 압박 수준
  MemoryPressureLevel get currentPressureLevel => _currentPressureLevel;

  /// 현재 최적화 전략
  MemoryOptimizationStrategy get currentStrategy => _currentStrategy;

  /// 통계 정보
  Map<String, dynamic> getStats() {
    final cacheStats = getCacheStats();
    return {
      'currentPressureLevel': _currentPressureLevel.name,
      'currentStrategy': _currentStrategy.name,
      'cacheSize': _cache.length,
      'maxCacheSize': config.maxCacheSize,
      'memoryHistorySize': _memoryHistory.length,
      'lastMemoryUsage': _lastMemoryUsage?.toString(),
      'monitoringActive': _monitoringTimer?.isActive ?? false,
      'leakDetectionActive': _leakDetectionTimer?.isActive ?? false,
      'trackedReferences': _trackedReferences.length,
      'cacheStats': cacheStats,
    };
  }

  /// 메모리 사용량 수동 확인
  Future<MemoryUsage> checkMemoryUsage() async {
    final usage = await _getCurrentMemoryUsage();
    _lastMemoryUsage = usage;
    _memoryHistory.add(usage);
    
    // 히스토리 크기 제한 (최근 100개)
    if (_memoryHistory.length > 100) {
      _memoryHistory.removeAt(0);
    }
    
    return usage;
  }

  /// 수동 최적화 수행
  Future<void> performOptimization(MemoryOptimizationStrategy strategy) async {
    _currentStrategy = strategy;
    
    switch (strategy) {
      case MemoryOptimizationStrategy.aggressive:
        await _performAggressiveOptimization();
        break;
      case MemoryOptimizationStrategy.conservative:
        await _performConservativeOptimization();
        break;
      case MemoryOptimizationStrategy.adaptive:
        await _performAdaptiveOptimization();
        break;
      case MemoryOptimizationStrategy.none:
        break;
    }
  }

  /// 리소스 정리
  void dispose() {
    _monitoringTimer?.cancel();
    _leakDetectionTimer?.cancel();
    _clearAllCache();
    _memoryHistory.clear();
    
    // 추적 중인 참조들 정리
    _trackedReferences.clear();
    _referenceTimestamps.clear();
    
    LoggerUtils.logDebug(
      'Memory manager disposed',
      tag: 'MemoryManager',
    );
  }

  /// 정적 리소스 정리 (앱 종료 시 호출)
  static void shutdown() {
    // MemoryManager는 인스턴스 기반이므로 개별적으로 dispose 호출 필요
    LoggerUtils.logInfo('MemoryManager 정리 완료');
  }
} 