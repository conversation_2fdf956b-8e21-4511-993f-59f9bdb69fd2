import 'package:freezed_annotation/freezed_annotation.dart';

part 'prepayment_product_link.freezed.dart';
part 'prepayment_product_link.g.dart';

@freezed
abstract class PrepaymentProductLink with _$PrepaymentProductLink {
  const factory PrepaymentProductLink({
    required int virtualProductId,
    required int productId,
    required DateTime linkedAt,
    @Default(1) int quantity,
    required int eventId, // 행사 ID 추가
  }) = _PrepaymentProductLink;

  factory PrepaymentProductLink.fromJson(Map<String, dynamic> json) => _$PrepaymentProductLinkFromJson(json);

  factory PrepaymentProductLink.fromMap(Map<String, dynamic> map) {
    return PrepaymentProductLink(
      virtualProductId: map['virtualProductId'] as int,
      productId: map['productId'] as int,
      linkedAt: DateTime.parse(map['linkedAt'] as String),
      quantity: map['quantity'] as int? ?? 1,
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
    );
  }
}

extension PrepaymentProductLinkExtension on PrepaymentProductLink {
  Map<String, dynamic> toDatabaseMap() {
    return {
      'virtualProductId': virtualProductId,
      'productId': productId,
      'linkedAt': linkedAt.toIso8601String(),
      'quantity': quantity,
      'eventId': eventId,
    };
  }
}

// SQLite 맵 변환을 위한 Extension (freezed 구조와 충돌 방지)
extension PrepaymentProductLinkMapper on PrepaymentProductLink {
  Map<String, dynamic> toMap() {
    return {
      'virtualProductId': virtualProductId,
      'productId': productId,
      'linkedAt': linkedAt.toIso8601String(),
      'quantity': quantity,
    };
  }
} 