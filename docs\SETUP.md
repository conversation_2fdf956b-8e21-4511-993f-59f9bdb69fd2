# Blue Booth Manager - 개발 환경 설정 가이드

## 📋 개요

이 문서는 Blue Booth Manager 프로젝트의 개발 환경을 설정하는 방법을 안내합니다. Flutter 개발 환경부터 프로젝트 실행까지 모든 단계를 포함합니다.

## 🔧 필수 요구사항

### 시스템 요구사항
- **운영체제**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **RAM**: 최소 8GB (권장 16GB)
- **저장공간**: 최소 10GB 여유 공간
- **CPU**: 멀티코어 프로세서 (권장 4코어 이상)

### 개발 도구
- **Flutter SDK**: 3.8.0 이상
- **Dart SDK**: 3.8.1 이상
- **IDE**: VS Code 또는 Android Studio
- **Git**: 2.0.0 이상

## 🚀 설치 및 설정

### 1. Flutter SDK 설치

#### Windows
1. [Flutter 공식 사이트](https://flutter.dev/docs/get-started/install/windows)에서 Flutter SDK 다운로드
2. 압축 해제 후 원하는 경로에 설치 (예: `C:\flutter`)
3. 환경 변수 설정:
   - `Path`에 `C:\flutter\bin` 추가
4. 명령 프롬프트에서 확인:
   ```bash
   flutter --version
   ```

#### macOS
1. [Flutter 공식 사이트](https://flutter.dev/docs/get-started/install/macos)에서 Flutter SDK 다운로드
2. 압축 해제 후 원하는 경로에 설치 (예: `~/flutter`)
3. 환경 변수 설정:
   ```bash
   export PATH="$PATH:`pwd`/flutter/bin"
   ```
4. 터미널에서 확인:
   ```bash
   flutter --version
   ```

#### Linux (Ubuntu)
1. [Flutter 공식 사이트](https://flutter.dev/docs/get-started/install/linux)에서 Flutter SDK 다운로드
2. 압축 해제 후 원하는 경로에 설치 (예: `~/flutter`)
3. 환경 변수 설정:
   ```bash
   export PATH="$PATH:`pwd`/flutter/bin"
   ```
4. 터미널에서 확인:
   ```bash
   flutter --version
   ```

### 2. IDE 설정

#### VS Code (권장)
1. [VS Code 다운로드](https://code.visualstudio.com/)
2. Flutter 확장 설치:
   - VS Code에서 `Ctrl+Shift+X` (Windows/Linux) 또는 `Cmd+Shift+X` (macOS)
   - "Flutter" 검색 후 설치
   - "Dart" 확장도 함께 설치

#### Android Studio
1. [Android Studio 다운로드](https://developer.android.com/studio)
2. Flutter 플러그인 설치:
   - `File` → `Settings` → `Plugins`
   - "Flutter" 검색 후 설치
   - "Dart" 플러그인도 함께 설치

### 3. 플랫폼별 설정

#### Android 개발 환경
1. Android Studio 설치
2. Android SDK 설치:
   - Android Studio에서 `Tools` → `SDK Manager`
   - Android SDK Platform 33 이상 설치
   - Android SDK Build-Tools 설치
3. Android 에뮬레이터 설정:
   - `Tools` → `AVD Manager`
   - 새 가상 디바이스 생성

#### iOS 개발 환경 (macOS만)
1. Xcode 설치 (App Store에서 다운로드)
2. Xcode Command Line Tools 설치:
   ```bash
   xcode-select --install
   ```
3. iOS 시뮬레이터 설정:
   - Xcode에서 `Window` → `Devices and Simulators`
   - 시뮬레이터 선택

#### Web 개발 환경
1. Chrome 브라우저 설치
2. Flutter web 지원 활성화:
   ```bash
   flutter config --enable-web
   ```

## 📦 프로젝트 설정

### 1. 프로젝트 클론
```bash
git clone https://github.com/your-username/parabara.git
cd parabara
```

### 2. 의존성 설치
```bash
flutter pub get
```

### 3. 코드 생성 실행
```bash
flutter packages pub run build_runner build
```

### 4. 개발 환경 확인
```bash
flutter doctor
```

모든 항목이 ✅로 표시되어야 합니다.

## 🔧 개발 환경 최적화

### 1. Flutter 설정 최적화
```bash
# 웹 지원 활성화
flutter config --enable-web

# 데스크톱 지원 활성화
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop

# 성능 최적화
flutter config --enable-impeller
```

### 2. IDE 설정 최적화

#### VS Code 설정
`.vscode/settings.json` 파일 생성:
```json
{
  "dart.flutterSdkPath": "C:\\flutter",
  "dart.lineLength": 80,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000
}
```

#### Android Studio 설정
- `File` → `Settings` → `Editor` → `Code Style` → `Dart`
- Line length: 80
- Enable formatter on save: 체크

### 3. Git 설정
```bash
# Git 사용자 정보 설정
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Git 훅 설정 (선택사항)
cp scripts/pre-commit.sh .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

## 🚀 프로젝트 실행

### 1. 디버그 모드 실행
```bash
# Android 에뮬레이터에서 실행
flutter run

# 특정 디바이스에서 실행
flutter devices
flutter run -d <device-id>

# 웹에서 실행
flutter run -d chrome

# 데스크톱에서 실행
flutter run -d windows  # Windows
flutter run -d macos    # macOS
flutter run -d linux    # Linux
```

### 2. 릴리즈 모드 실행
```bash
# 릴리즈 빌드
flutter run --release

# 특정 플랫폼 릴리즈 빌드
flutter build apk --release      # Android
flutter build ios --release      # iOS
flutter build web --release      # Web
flutter build windows --release  # Windows
```

### 3. 테스트 실행
```bash
# 전체 테스트 실행
flutter test

# 특정 테스트 파일 실행
flutter test test/providers/product_provider_test.dart

# 커버리지와 함께 테스트 실행
flutter test --coverage
```

## 🔍 문제 해결

### 일반적인 문제들

#### 1. Flutter Doctor 오류
```bash
# Flutter 캐시 정리
flutter clean
flutter pub get

# Flutter 업데이트
flutter upgrade
```

#### 2. 의존성 충돌
```bash
# pubspec.lock 삭제 후 재설치
rm pubspec.lock
flutter pub get
```

#### 3. 빌드 오류
```bash
# 빌드 캐시 정리
flutter clean
flutter pub get
flutter run
```

#### 4. Android 빌드 오류
```bash
# Android 빌드 캐시 정리
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get
```

#### 5. iOS 빌드 오류
```bash
# iOS 빌드 캐시 정리
cd ios
rm -rf Pods Podfile.lock
pod install
cd ..
flutter clean
flutter pub get
```

### 플랫폼별 문제 해결

#### Android
- **SDK 경로 문제**: Android Studio에서 SDK 경로 확인
- **에뮬레이터 문제**: AVD Manager에서 에뮬레이터 재생성
- **권한 문제**: AndroidManifest.xml에서 권한 확인

#### iOS
- **Xcode 버전 문제**: 최신 Xcode 버전 사용
- **시뮬레이터 문제**: Xcode에서 시뮬레이터 재설정
- **서명 문제**: 개발자 계정 설정 확인

#### Web
- **Chrome 문제**: 최신 Chrome 브라우저 사용
- **포트 문제**: 다른 포트 사용 `flutter run -d chrome --web-port 8080`

## 📱 디바이스 연결

### Android 디바이스
1. 개발자 옵션 활성화
2. USB 디버깅 활성화
3. USB로 연결
4. 디바이스 확인:
   ```bash
   flutter devices
   ```

### iOS 디바이스
1. 개발자 계정 필요
2. Xcode에서 디바이스 등록
3. USB로 연결
4. 신뢰 설정

### 물리적 디바이스 테스트
```bash
# 연결된 디바이스 확인
flutter devices

# 특정 디바이스에서 실행
flutter run -d <device-id>
```

## 🔧 개발 도구

### 1. Flutter Inspector
- VS Code: `Ctrl+Shift+P` → "Flutter: Open Inspector"
- Android Studio: `View` → `Tool Windows` → `Flutter Inspector`

### 2. Performance Overlay
```dart
// main.dart에서 활성화
import 'package:flutter/services.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
  runApp(MyApp());
}
```

### 3. Debug Console
- VS Code: `View` → `Debug Console`
- Android Studio: `View` → `Tool Windows` → `Debug`

## 📊 성능 모니터링

### 1. 메모리 사용량 모니터링
```bash
# 메모리 사용량 확인
flutter run --profile
```

### 2. 성능 분석
```bash
# 성능 프로파일링
flutter run --profile --trace-startup
```

### 3. 빌드 시간 최적화
```bash
# 병렬 빌드 활성화
flutter build apk --parallel
```

## 🔒 보안 설정

### 1. API 키 관리
```dart
// .env 파일 사용 (pubspec.yaml에 flutter_dotenv 추가)
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() async {
  await dotenv.load(fileName: ".env");
  runApp(MyApp());
}
```

### 2. 디버그 모드 비활성화
```bash
# 릴리즈 빌드에서 디버그 기능 비활성화
flutter run --release
```

## 📝 개발 가이드라인

### 1. 코드 스타일
- Dart 공식 스타일 가이드 준수
- `dart format` 사용
- 80자 라인 길이 제한

### 2. 커밋 메시지
```
feat: 새로운 기능 추가
fix: 버그 수정
docs: 문서 수정
style: 코드 스타일 수정
refactor: 코드 리팩토링
test: 테스트 추가/수정
chore: 빌드 프로세스 수정
```

### 3. 브랜치 전략
- `main`: 프로덕션 브랜치
- `develop`: 개발 브랜치
- `feature/기능명`: 기능 개발 브랜치
- `hotfix/버그명`: 긴급 수정 브랜치

## 🆘 지원 및 문의

### 문제 발생 시
1. 이 문서의 문제 해결 섹션 확인
2. Flutter 공식 문서 확인
3. GitHub Issues에서 유사한 문제 검색
4. 팀 내 기술 지원 요청

### 유용한 링크
- [Flutter 공식 문서](https://flutter.dev/docs)
- [Dart 언어 가이드](https://dart.dev/guides)
- [Riverpod 문서](https://riverpod.dev/)
- [SQLite 문서](https://www.sqlite.org/docs.html)

---

**작성자**: Blue  
**버전**: 1.0.0  
**최종 업데이트**: 2025년 1월 