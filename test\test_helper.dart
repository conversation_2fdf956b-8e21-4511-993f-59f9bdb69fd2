import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:parabara/repositories/settings_repository.dart';
import 'package:parabara/providers/settings_provider.dart';
import 'package:parabara/providers/base_state.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

// Mock 생성 - customMocks를 사용하여 이름 충돌 방지
@GenerateMocks([], customMocks: [
  MockSpec<SettingsRepository>(as: #MockSettingsRepositoryForTest)
])

/// 테스트 환경 최적화 클래스
class TestEnvironmentOptimizer {
  static bool _isInitialized = false;

  /// 테스트 환경 초기화
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _initializeTestOptimizations();
      _isInitialized = true;
    } catch (e) {
      print('Test environment initialization failed: $e');
      rethrow; // 테스트 실패를 명확히 하기 위해 예외를 다시 던짐
    }
  }
  
  /// 테스트용 최적화 시스템 초기화
  static Future<void> _initializeTestOptimizations() async {
    // 테스트 환경에서 필요한 기본 설정
    WidgetsFlutterBinding.ensureInitialized();
    
    // sqflite_common_ffi 환경 초기화
    databaseFactory = databaseFactoryFfi;

    // 테스트용 타임아웃 설정
    await Future.delayed(Duration.zero);
  }
  
  /// 테스트 환경 정리
  static Future<void> cleanup() async {
    if (!_isInitialized) return;

    try {
      await _cleanupTestOptimizations();
      _isInitialized = false;
    } catch (e) {
      print('Test environment cleanup failed: $e');
      rethrow;
    }
  }
  
  /// 테스트용 최적화 시스템 정리
  static Future<void> _cleanupTestOptimizations() async {
    TestLogger.clear();
  }

  /// 테스트 간 격리를 위한 상태 리셋
  static void reset() {
    _isInitialized = false;
  }
}

/// 테스트용 Provider Container 설정
class TestProviderContainer {
  static ProviderContainer create({
    SettingsRepository? settingsRepository,
    Map<String, dynamic>? initialSettings,
  }) {
    final overrides = <Override>[];
    
    // Settings Repository Override
    if (settingsRepository != null) {
      overrides.add(
        settingsRepositoryProvider.overrideWithValue(settingsRepository)
      );
      
      // 초기 설정값이 제공된 경우 mock 설정
      if (initialSettings != null) {
        // 각 설정값에 대한 mock 설정
        initialSettings.forEach((key, value) {
          if (value is String) {
            when(settingsRepository.getString(key))
                .thenAnswer((_) async => value);
          } else if (value is int) {
            when(settingsRepository.getInt(key))
                .thenAnswer((_) async => value);
          } else if (value is bool) {
            when(settingsRepository.getBool(key))
                .thenAnswer((_) async => value);
          } else if (value is double) {
            when(settingsRepository.getDouble(key))
                .thenAnswer((_) async => value);
          } else if (value is List<String>) {
            when(settingsRepository.getStringList(key))
                .thenAnswer((_) async => value);
          }
        });
      }
    }
    
    final container = ProviderContainer(overrides: overrides);
    
    // 컨테이너 생성 후 초기화 대기
    Future.microtask(() async {
      await CustomTestAsyncUtils.waitForProviderStabilization();
    });
    
    return container;
  }
  
  /// 테스트 완료 후 컨테이너 정리
  static Future<void> dispose(ProviderContainer container) async {
    await CustomTestAsyncUtils.waitForProviderStabilization();
    container.dispose();
  }
}

/// 테스트용 기본 설정 상태
class TestSettings {
  static const defaultSettings = {
    'event_day_of_week': 7,
    'enabled_days_of_week': ['1', '2', '3', '4', '5', '6', '7'],
    'collect_day_of_week_from_excel': false,
    'excel_day_of_week_column_index': -1,
    'link_prepayment_to_inventory': false,
    'inventory_columns': 3,
    'sale_columns': 3,
    'inventory_columns_portrait': 3,
    'inventory_columns_landscape': 5,
    'sale_columns_portrait': 3,
    'sale_columns_landscape': 5,
    'is_simple_view_mode': false,
  };
}

/// 테스트용 비동기 작업 대기
class CustomTestAsyncUtils {
  /// 비동기 작업이 완료될 때까지 대기
  static Future<void> waitForAsync() async {
    await Future.delayed(const Duration(milliseconds: 300)); // 대기 시간 증가
  }
  
  /// 특정 조건이 만족될 때까지 대기
  static Future<void> waitForCondition(
    Future<bool> Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final startTime = DateTime.now();
    
    while (DateTime.now().difference(startTime) < timeout) {
      if (await condition()) {
        return;
      }
      await Future.delayed(interval);
    }
    
    throw TimeoutException('Condition not met within timeout', timeout);
  }
  
  /// Provider 상태 안정화 대기
  static Future<void> waitForProviderStabilization() async {
    await Future.delayed(const Duration(milliseconds: 500)); // 대기 시간 증가
  }
  
  /// 에러 처리 완료 대기
  static Future<void> waitForErrorHandling() async {
    await Future.delayed(const Duration(milliseconds: 500)); // 대기 시간 증가
  }
}

/// 테스트용 로깅 유틸리티
class TestLogger {
  static final List<String> _logs = [];
  
  static void log(String message) {
    _logs.add('${DateTime.now()}: $message');
  }
  
  static List<String> getLogs() => List.from(_logs);
  
  static void clear() => _logs.clear();
  
  static void printLogs() {
    for (final log in _logs) {
      print(log);
    }
  }
}

/// 테스트용 예외 클래스
class TestException implements Exception {
  final String message;
  final String? code;
  
  TestException(this.message, {this.code});
  
  @override
  String toString() => 'TestException: $message${code != null ? ' (code: $code)' : ''}';
}

/// 테스트용 타임아웃 예외
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;
  
  TimeoutException(this.message, this.timeout);
  
  @override
  String toString() => 'TimeoutException: $message (timeout: ${timeout.inMilliseconds}ms)';
} 

/// 폴링 유틸리티 클래스
class PollingUtils {
  /// 조건이 만족될 때까지 폴링
  static Future<bool> waitForCondition<T>(
    Future<bool> Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final startTime = DateTime.now();
    
    while (DateTime.now().difference(startTime) < timeout) {
      if (await condition()) {
        return true;
      }
      await Future.delayed(interval);
    }
    
    return false;
  }

  /// Provider의 상태가 특정 값이 될 때까지 대기
  static Future<bool> waitForProviderState<T extends ChangeNotifier>(
    ChangeNotifierProvider<T> provider,
    bool Function(T state) condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    return waitForCondition(
      () async {
        // Provider 상태 확인 로직은 실제 사용 시 구현
        return false;
      },
      timeout: timeout,
      interval: interval,
    );
  }

  /// SettingsProvider의 특정 설정이 변경될 때까지 대기
  static Future<bool> waitForSettingsChange(
    SettingsNotifier provider,
    bool Function(SettingsNotifier) condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    return waitForCondition(
      () async => condition(provider),
      timeout: timeout,
      interval: interval,
    );
  }

  /// 설정 값이 특정 값과 일치할 때까지 대기
  static Future<bool> waitForSettingValue<T>(
    SettingsNotifier provider,
    T expectedValue,
    T Function(SettingsNotifier) getter, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    return waitForCondition(
      () async => getter(provider) == expectedValue,
      timeout: timeout,
      interval: interval,
    );
  }
}

/// 테스트용 설정 검증 헬퍼
class SettingsTestHelper {
  /// 설정 변경 후 안정화 대기
  static Future<void> waitForSettingsStabilization(
    SettingsNotifier provider, {
    Duration timeout = const Duration(seconds: 3),
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));
  }

  /// 설정 변경 후 검증
  static Future<void> changeAndVerifySetting<T>(
    SettingsNotifier provider,
    Future<void> Function() changeAction,
    T expectedValue,
    T Function(SettingsNotifier) getter, {
    Duration timeout = const Duration(seconds: 5),
    Duration stabilizationDelay = const Duration(milliseconds: 200),
  }) async {
    await changeAction();
    await Future.delayed(stabilizationDelay);
    
    final success = await PollingUtils.waitForSettingValue(
      provider,
      expectedValue,
      getter,
      timeout: timeout,
    );
    
    if (!success) {
      throw TestException(
        'Setting verification failed: expected $expectedValue, got ${getter(provider)}',
      );
    }
  }
}

/// 테스트용 에러 처리 헬퍼
class ErrorTestHelper {
  /// 에러 발생을 기대하는 테스트 헬퍼
  static Future<void> expectError(
    Future<void> Function() action, {
    String? expectedErrorMessage,
    String? expectedErrorCode,
  }) async {
    try {
      await action();
      // 에러가 발생하지 않으면 테스트 실패
      throw TestException('Expected error but none occurred');
    } catch (e) {
      if (expectedErrorMessage != null) {
        expect(e.toString(), contains(expectedErrorMessage));
      }
      if (expectedErrorCode != null) {
        expect(e.toString(), contains(expectedErrorCode));
      }
    }
  }
  
  /// 에러 상태 검증 헬퍼
  static void verifyErrorState(
    dynamic state, {
    String? expectedErrorMessage,
    String? expectedErrorCode,
  }) {
    if (expectedErrorMessage != null) {
      expect(state.errorMessage, isNotNull);
      expect(state.errorMessage.toString(), contains(expectedErrorMessage));
    }
    if (expectedErrorCode != null) {
      expect(state.errorCode, isNotNull);
      expect(state.errorCode.toString(), contains(expectedErrorCode));
    }
  }
}

/// 테스트용 Provider 헬퍼
class ProviderTestHelper {
  /// Provider 상태 안정화 대기
  static Future<void> waitForProviderStabilization() async {
    await Future.delayed(const Duration(milliseconds: 200));
  }
  
  /// Provider 에러 처리 대기
  static Future<void> waitForErrorHandling() async {
    await Future.delayed(const Duration(milliseconds: 300));
  }
  
  /// Provider 상태 검증
  static void verifyProviderState(
    dynamic state, {
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
  }) {
    if (isLoading != null) {
      expect(state.isLoading, equals(isLoading));
    }
    if (errorMessage != null) {
      expect(state.errorMessage, equals(errorMessage));
    }
    if (errorCode != null) {
      expect(state.errorCode, equals(errorCode));
    }
  }
}

/// 테스트용 성능 헬퍼
class PerformanceTestHelper {
  /// 성능 테스트 실행
  static Future<Duration> measureExecutionTime(
    Future<void> Function() action,
  ) async {
    final stopwatch = Stopwatch()..start();
    await action();
    stopwatch.stop();
    return stopwatch.elapsed;
  }
  
  /// 성능 테스트 검증
  static void verifyPerformance(
    Duration executionTime, {
    Duration? maxDuration,
  }) {
    if (maxDuration != null) {
      expect(executionTime, lessThan(maxDuration));
    }
  }
} 

/// Provider 테스트용 공통 에러 처리 헬퍼
class ProviderErrorTestHelper {
  /// Provider의 에러 상태가 설정될 때까지 대기
  static Future<void> waitForErrorState<T extends BaseState>(
    ProviderContainer container,
    StateNotifierProvider<StateNotifier<T>, T> provider, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final deadline = DateTime.now().add(timeout);
    while (DateTime.now().isBefore(deadline)) {
      final state = container.read(provider);
      if (state.errorMessage != null) {
        return;
      }
      await Future.delayed(interval);
    }
    
    // 마지막 상태 확인
    final finalState = container.read(provider);
    expect(
      finalState.errorMessage, 
      isNotNull, 
      reason: 'Provider error state not set within timeout. Final state: ${finalState.toString()}'
    );
  }

  /// 에러 발생을 기대하는 작업 실행 및 상태 검증
  static Future<void> expectErrorOperation<T extends BaseState>(
    ProviderContainer container,
    StateNotifierProvider<StateNotifier<T>, T> provider,
    Future<void> Function() operation, {
    Duration timeout = const Duration(seconds: 5),
    String? expectedErrorMessage,
    String? expectedErrorCode,
  }) async {
    try {
      await operation();
      // 에러가 발생하지 않으면 테스트 실패
      fail('Expected error but none occurred');
    } catch (e) {
      // 예외 발생은 정상 - 상태 업데이트 대기
      await Future.delayed(const Duration(milliseconds: 200)); // 상태 업데이트 대기
      
      // 에러 상태 확인
      final state = container.read(provider);
      if (state.errorMessage == null) {
        // 에러 상태가 설정되지 않았다면 추가 대기
        await waitForErrorState(container, provider, timeout: timeout);
      }
      
      // 최종 상태 검증
      final finalState = container.read(provider);
      if (expectedErrorMessage != null) {
        expect(
          finalState.errorMessage, 
          contains(expectedErrorMessage),
          reason: 'Expected error message containing "$expectedErrorMessage" but got "${finalState.errorMessage}"'
        );
      }
      if (expectedErrorCode != null) {
        expect(
          finalState.errorCode, 
          contains(expectedErrorCode),
          reason: 'Expected error code containing "$expectedErrorCode" but got "${finalState.errorCode}"'
        );
      }
    }
  }

  /// Provider 상태 검증 헬퍼
  static void verifyProviderState<T extends BaseState>(
    T state, {
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    bool? hasError,
  }) {
    if (isLoading != null) {
      expect(state.isLoading, equals(isLoading));
    }
    if (errorMessage != null) {
      expect(state.errorMessage, equals(errorMessage));
    }
    if (errorCode != null) {
      expect(state.errorCode, equals(errorCode));
    }
    if (hasError != null) {
      expect(state.hasError, equals(hasError));
    }
  }

  /// Provider 상태가 안정화될 때까지 대기
  static Future<void> waitForProviderStabilization<T extends BaseState>(
    ProviderContainer container,
    StateNotifierProvider<StateNotifier<T>, T> provider, {
    Duration timeout = const Duration(seconds: 2),
  }) async {
    final deadline = DateTime.now().add(timeout);
    T? lastState;
    
    while (DateTime.now().isBefore(deadline)) {
      final currentState = container.read(provider);
      if (lastState != null && lastState == currentState) {
        // 상태가 안정화됨
        return;
      }
      lastState = currentState;
      await Future.delayed(const Duration(milliseconds: 50));
    }
  }
} 