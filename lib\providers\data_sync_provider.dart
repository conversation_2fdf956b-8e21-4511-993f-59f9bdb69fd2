import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/data_sync_service.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 데이터 동기화 상태를 나타내는 열거형
enum SyncStatus {
  idle,
  syncing,
  success,
  error,
}

/// 데이터 동기화 상태 클래스
class DataSyncState {
  final SyncStatus status;
  final String? message;
  final String? errorMessage;
  final double? progress; // 0.0 ~ 1.0

  const DataSyncState({
    this.status = SyncStatus.idle,
    this.message,
    this.errorMessage,
    this.progress,
  });

  DataSyncState copyWith({
    SyncStatus? status,
    String? message,
    String? errorMessage,
    double? progress,
  }) {
    return DataSyncState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
      progress: progress ?? this.progress,
    );
  }

  bool get isLoading => status == SyncStatus.syncing;
  bool get hasError => status == SyncStatus.error;
  bool get isSuccess => status == SyncStatus.success;
}

/// 데이터 동기화 Provider
class DataSyncNotifier extends StateNotifier<DataSyncState> {
  static const String _tag = 'DataSyncNotifier';
  
  final DataSyncService _syncService;

  DataSyncNotifier(this._syncService) : super(const DataSyncState());

  /// 서버에 데이터가 있는지 확인
  Future<bool> hasServerData() async {
    try {
      return await _syncService.hasServerData();
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 전체 데이터 업로드
  Future<void> uploadAllData() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '업로드를 시작합니다...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      double currentProgress = 0.0;
      await _syncService.uploadAllData(
        onProgress: (message) {
          // 진행률 추정 (메시지 기반)
          if (message.contains('행사 데이터')) {
            currentProgress = 0.1;
          } else if (message.contains('%')) {
            // 퍼센트가 포함된 메시지에서 진행률 추출
            final match = RegExp(r'(\d+)%').firstMatch(message);
            if (match != null) {
              final percent = int.parse(match.group(1)!);
              currentProgress = 0.1 + (percent / 100.0 * 0.8); // 10% ~ 90%
            }
          } else if (message.contains('완료')) {
            currentProgress = 1.0;
          }

          state = state.copyWith(
            message: message,
            progress: currentProgress,
          );
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '모든 데이터가 성공적으로 업로드되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('전체 데이터 업로드 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '업로드 실패: $e',
      );
      LoggerUtils.logError('전체 데이터 업로드 실패', tag: _tag, error: e);
    }
  }

  /// 재고현황 데이터만 업로드
  Future<void> uploadInventoryData() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '재고현황 데이터를 업로드하는 중...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      await _syncService.uploadInventoryData(
        onProgress: (message) {
          state = state.copyWith(message: message);
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '재고현황 데이터가 성공적으로 업로드되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('재고현황 데이터 업로드 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '재고현황 업로드 실패: $e',
      );
      LoggerUtils.logError('재고현황 데이터 업로드 실패', tag: _tag, error: e);
    }
  }

  /// 선입금 데이터만 업로드
  Future<void> uploadPrepaymentData() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '선입금 데이터를 업로드하는 중...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      await _syncService.uploadPrepaymentData(
        onProgress: (message) {
          state = state.copyWith(message: message);
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '선입금 데이터가 성공적으로 업로드되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('선입금 데이터 업로드 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '선입금 업로드 실패: $e',
      );
      LoggerUtils.logError('선입금 데이터 업로드 실패', tag: _tag, error: e);
    }
  }

  /// 전체 데이터 다운로드
  Future<void> downloadAllData() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '다운로드를 시작합니다...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      await _syncService.downloadAllData(
        onProgress: (message) {
          // 더 정확한 진행률 추정
          double currentProgress = 0.0;
          
          if (message.contains('시작합니다')) {
            currentProgress = 0.05; // 5%
          } else if (message.contains('행사 데이터를 다운로드하는 중')) {
            currentProgress = 0.1; // 10%
          } else if (message.contains('%')) {
            // 퍼센트가 포함된 메시지에서 진행률 추출
            final match = RegExp(r'(\d+)%').firstMatch(message);
            if (match != null) {
              final percent = int.parse(match.group(1)!);
              // 10% ~ 95% 범위로 매핑 (마지막 5%는 완료 처리용)
              currentProgress = 0.1 + (percent / 100.0 * 0.85);
            }
          } else if (message.contains('완료')) {
            currentProgress = 1.0; // 100%
          }

          state = state.copyWith(
            message: message,
            progress: currentProgress,
          );
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '모든 데이터가 성공적으로 다운로드되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('전체 데이터 다운로드 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '다운로드 실패: $e',
      );
      LoggerUtils.logError('전체 데이터 다운로드 실패', tag: _tag, error: e);
    }
  }

  /// 재고현황 데이터만 다운로드
  Future<void> downloadInventoryData() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '재고현황 데이터를 다운로드하는 중...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      await _syncService.downloadInventoryData(
        onProgress: (message) {
          state = state.copyWith(message: message);
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '재고현황 데이터가 성공적으로 다운로드되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('재고현황 데이터 다운로드 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '재고현황 다운로드 실패: $e',
      );
      LoggerUtils.logError('재고현황 데이터 다운로드 실패', tag: _tag, error: e);
    }
  }

  /// 선입금 데이터만 다운로드
  Future<void> downloadPrepaymentData() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '선입금 데이터를 다운로드하는 중...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      await _syncService.downloadPrepaymentData(
        onProgress: (message) {
          state = state.copyWith(message: message);
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '선입금 데이터가 성공적으로 다운로드되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('선입금 데이터 다운로드 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '선입금 다운로드 실패: $e',
      );
      LoggerUtils.logError('선입금 데이터 다운로드 실패', tag: _tag, error: e);
    }
  }



  /// 양방향 동기화 (스마트 동기화)
  Future<void> performBidirectionalSync() async {
    if (!_syncService.isUserLoggedIn) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '로그인이 필요합니다.',
      );
      return;
    }

    state = state.copyWith(
      status: SyncStatus.syncing,
      message: '양방향 동기화를 시작합니다...',
      errorMessage: null,
      progress: 0.0,
    );

    try {
      await _syncService.performBidirectionalSync(
        onProgress: (message) {
          double currentProgress = 0.0;
          if (message.contains('%')) {
            final match = RegExp(r'(\d+)%').firstMatch(message);
            if (match != null) {
              final percent = int.parse(match.group(1)!);
              currentProgress = percent / 100.0;
            }
          } else if (message.contains('완료')) {
            currentProgress = 1.0;
          }

          state = state.copyWith(
            message: message,
            progress: currentProgress,
          );
        },
        onError: (error) {
          state = state.copyWith(
            status: SyncStatus.error,
            errorMessage: error,
          );
        },
      );

      state = state.copyWith(
        status: SyncStatus.success,
        message: '양방향 동기화가 성공적으로 완료되었습니다!',
        progress: 1.0,
      );

      LoggerUtils.logInfo('양방향 동기화 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        status: SyncStatus.error,
        errorMessage: '양방향 동기화 실패: $e',
      );
      LoggerUtils.logError('양방향 동기화 실패', tag: _tag, error: e);
    }
  }

  /// 상태 초기화
  void resetState() {
    state = const DataSyncState();
  }

  /// 에러 상태 클리어
  void clearError() {
    state = state.copyWith(
      status: SyncStatus.idle,
      errorMessage: null,
    );
  }
}

/// DataSyncService Provider
final dataSyncServiceProvider = Provider<DataSyncService>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return DataSyncService(databaseService);
});

/// DataSyncNotifier Provider
final dataSyncProvider = StateNotifierProvider<DataSyncNotifier, DataSyncState>((ref) {
  final syncService = ref.watch(dataSyncServiceProvider);
  return DataSyncNotifier(syncService);
});
