#!/bin/bash

# Blue Booth Manager - Pre-commit Hook
# Git 커밋 전 자동 검사를 수행하는 스크립트

set -e

echo "🔍 Pre-commit 검사 시작..."

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 로그 함수
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 코드 포맷 검사
log_info "1. 코드 포맷 검사..."
if ! dart format --set-exit-if-changed .; then
    log_error "코드 포맷 문제 발견!"
    log_info "다음 명령으로 수정하세요: dart format ."
    exit 1
fi
log_success "코드 포맷 검사 통과"

# 2. 코드 분석
log_info "2. 코드 분석..."
if ! flutter analyze; then
    log_error "코드 분석 실패!"
    exit 1
fi
log_success "코드 분석 통과"

# 3. 빠른 테스트 실행 (주요 테스트만)
log_info "3. 빠른 테스트 실행..."
if ! flutter test test/utils/memory_manager_test.dart; then
    log_error "메모리 관리 테스트 실패!"
    exit 1
fi

if ! flutter test test/utils/batch_processor_test.dart; then
    log_error "배치 프로세서 테스트 실패!"
    exit 1
fi

if ! flutter test test/providers/; then
    log_error "Provider 테스트 실패!"
    exit 1
fi

log_success "빠른 테스트 통과"

# 4. 커스텀 린트 검사
log_info "4. 커스텀 린트 검사..."
if [ -f "custom_lint.log" ]; then
    if grep -q "ERROR" custom_lint.log; then
        log_error "커스텀 린트 오류 발견:"
        grep "ERROR" custom_lint.log
        exit 1
    fi
fi
log_success "커스텀 린트 검사 통과"

echo ""
log_success "🎉 Pre-commit 검사 완료! 커밋을 진행합니다." 