import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../utils/app_colors.dart';
import '../utils/dimens.dart';

/// 모던하고 미니멀한 다이얼로그 컴포넌트들을 제공하는 클래스
class ModernDialogComponents {
  /// 모던한 다이얼로그 스타일을 적용한 AlertDialog를 생성합니다.
  static Widget buildModernDialog({
    required BuildContext context,
    required String title,
    required Widget content,
    List<Widget>? actions,
    double? width,
    double? maxHeight,
  }) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: width ?? MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 헤더
            Container(
              padding: const EdgeInsets.all(Dimens.space24),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(Dimens.radiusL),
                  topRight: Radius.circular(Dimens.radiusL),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontFamily: 'Pretendard',
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.onSurfaceVariant,
                      size: 24,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.neutral10,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(Dimens.radiusS),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 구분선
            Divider(
              height: 1,
              thickness: 1,
              color: AppColors.neutral20,
            ),
            // 콘텐츠
            Flexible(
              child: Container(
                padding: const EdgeInsets.all(Dimens.space24),
                child: content,
              ),
            ),
            // 액션 버튼들
            if (actions != null && actions.isNotEmpty) ...[
              Divider(
                height: 1,
                thickness: 1,
                color: AppColors.neutral20,
              ),
              Container(
                padding: const EdgeInsets.all(Dimens.space24),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 모던한 스타일의 선택 타일을 생성합니다.
  static Widget buildSelectionTile({
    required BuildContext context,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
    Widget? trailing,
    String? subtitle,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: Dimens.space4),
      decoration: BoxDecoration(
        color: isSelected 
            ? AppColors.primarySeed.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        border: Border.all(
          color: isSelected 
              ? AppColors.primarySeed.withValues(alpha: 0.3)
              : AppColors.neutral20,
          width: 1,
        ),
      ),
      child: ListTile(
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontFamily: 'Pretendard',
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? AppColors.primarySeed : AppColors.onSurface,
          ),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontFamily: 'Pretendard',
                  color: AppColors.onSurfaceVariant,
                ),
              )
            : null,
        trailing: trailing ?? (isSelected 
            ? Icon(
                Icons.check_circle,
                color: AppColors.primarySeed,
                size: 20,
              )
            : null),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(Dimens.radiusM),
        ),
      ),
    );
  }

  /// 모던한 스타일의 섹션 헤더를 생성합니다.
  static Widget buildSectionHeader({
    required BuildContext context,
    required String title,
    Widget? trailing,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: Dimens.space12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontFamily: 'Pretendard',
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
          ),
          if (trailing != null) trailing,
        ],
      ),
    );
  }

  /// 모던한 스타일의 버튼을 생성합니다.
  static Widget buildModernButton({
    required String text,
    required VoidCallback? onPressed,
    bool isPrimary = true,
    bool isTablet = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary ? AppColors.primarySeed : AppColors.surface,
        foregroundColor: isPrimary ? Colors.white : AppColors.onSurface,
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? Dimens.space24 : Dimens.space20,
          vertical: isTablet ? Dimens.space16 : Dimens.space12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(Dimens.radiusM),
          side: isPrimary
              ? BorderSide.none
              : BorderSide(color: AppColors.neutral30),
        ),
        elevation: isPrimary ? 2 : 0,
      ),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'Pretendard',
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 16 : 14,
        ),
      ),
    );
  }

  /// POS 스타일의 선택 타일을 생성합니다 (테두리 방식)
  static Widget buildPOSSelectionTile({
    required BuildContext context,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
    String? subtitle,
    Widget? trailing,
    bool isTablet = false,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: isTablet ? 6 : 4),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 16 : 12,
              vertical: isTablet ? 14 : 12,
            ),
            decoration: isSelected ? BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primarySeed,
                width: 2,
              ),
            ) : null,
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: isTablet ? 16 : 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.onSurface,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: isTablet ? 13 : 12,
                            fontWeight: FontWeight.w400,
                            color: isSelected
                                ? AppColors.primarySeed.withValues(alpha: 0.8)
                                : AppColors.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[
                  const SizedBox(width: 8),
                  trailing,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 홈탭 스타일의 미니멀한 달력 위젯
class MinimalCalendarWidget extends StatelessWidget {
  final DateTimeRange? initialDateRange;
  final DateTime? minDate;
  final DateTime? maxDate;
  final Function(DateTimeRange?)? onDateRangeChanged;
  final bool allowSingleDate;

  const MinimalCalendarWidget({
    super.key,
    this.initialDateRange,
    this.minDate,
    this.maxDate,
    this.onDateRangeChanged,
    this.allowSingleDate = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 320,
      height: 350,
      child: SfDateRangePicker(
        selectionMode: DateRangePickerSelectionMode.range,
        initialSelectedRange: initialDateRange != null
            ? PickerDateRange(initialDateRange!.start, initialDateRange!.end)
            : null,
        minDate: minDate,
        maxDate: maxDate,
        backgroundColor: AppColors.surface,
        todayHighlightColor: AppColors.primarySeed,
        selectionColor: AppColors.primarySeed,
        startRangeSelectionColor: AppColors.primarySeed,
        endRangeSelectionColor: AppColors.primarySeed,
        rangeSelectionColor: AppColors.primarySeed.withValues(alpha: 0.3),
        selectionTextStyle: TextStyle(
          color: AppColors.onPrimary,
          fontWeight: FontWeight.w500,
          fontFamily: 'Pretendard',
        ),
        rangeTextStyle: TextStyle(
          color: AppColors.onSurface,
          fontWeight: FontWeight.w400,
          fontFamily: 'Pretendard',
        ),
        headerStyle: DateRangePickerHeaderStyle(
          backgroundColor: AppColors.surface,
          textStyle: TextStyle(
            color: AppColors.onSurface,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Pretendard',
          ),
        ),
        monthViewSettings: DateRangePickerMonthViewSettings(
          firstDayOfWeek: 1, // 월요일부터 시작
          dayFormat: 'EEE',
          viewHeaderStyle: DateRangePickerViewHeaderStyle(
            backgroundColor: AppColors.surface,
            textStyle: TextStyle(
              color: AppColors.onSurfaceVariant,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              fontFamily: 'Pretendard',
            ),
          ),
        ),
        onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
          if (onDateRangeChanged != null) {
            if (args.value is PickerDateRange) {
              final range = args.value as PickerDateRange;
              if (range.startDate != null) {
                final endDate = range.endDate ?? 
                    (allowSingleDate ? range.startDate! : null);
                if (endDate != null) {
                  onDateRangeChanged!(DateTimeRange(
                    start: range.startDate!,
                    end: endDate,
                  ));
                }
              }
            }
          }
        },
      ),
    );
  }
}
