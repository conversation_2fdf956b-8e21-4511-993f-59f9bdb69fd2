/// 동기화 메타데이터 관리 서비스
/// 
/// 마지막 동기화 시간, 동기화 상태 등을 관리합니다.
/// SharedPreferences를 사용하여 로컬에 저장합니다.
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger_utils.dart';

/// 동기화 메타데이터
class SyncMetadata {
  final String collectionName;
  final int eventId;
  final DateTime lastSyncTime;
  final int lastSyncCount;
  final String lastSyncHash;
  
  const SyncMetadata({
    required this.collectionName,
    required this.eventId,
    required this.lastSyncTime,
    required this.lastSyncCount,
    required this.lastSyncHash,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'collectionName': collectionName,
      'eventId': eventId,
      'lastSyncTime': lastSyncTime.toIso8601String(),
      'lastSyncCount': lastSyncCount,
      'lastSyncHash': lastSyncHash,
    };
  }
  
  factory SyncMetadata.fromJson(Map<String, dynamic> json) {
    return SyncMetadata(
      collectionName: json['collectionName'] as String,
      eventId: json['eventId'] as int,
      lastSyncTime: DateTime.parse(json['lastSyncTime'] as String),
      lastSyncCount: json['lastSyncCount'] as int,
      lastSyncHash: json['lastSyncHash'] as String,
    );
  }
  
  SyncMetadata copyWith({
    String? collectionName,
    int? eventId,
    DateTime? lastSyncTime,
    int? lastSyncCount,
    String? lastSyncHash,
  }) {
    return SyncMetadata(
      collectionName: collectionName ?? this.collectionName,
      eventId: eventId ?? this.eventId,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      lastSyncCount: lastSyncCount ?? this.lastSyncCount,
      lastSyncHash: lastSyncHash ?? this.lastSyncHash,
    );
  }
}

class SyncMetadataService {
  static const String _tag = 'SyncMetadataService';
  static const String _keyPrefix = 'sync_metadata_';
  
  static SyncMetadataService? _instance;
  static SyncMetadataService get instance => _instance ??= SyncMetadataService._();
  
  SyncMetadataService._();
  
  SharedPreferences? _prefs;
  
  /// 초기화
  Future<void> initialize() async {
    try {
      _prefs ??= await SharedPreferences.getInstance();
      LoggerUtils.logInfo('동기화 메타데이터 서비스 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('동기화 메타데이터 서비스 초기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 특정 컬렉션의 마지막 동기화 시간 조회
  Future<DateTime?> getLastSyncTime(int eventId, String collectionName) async {
    await _ensureInitialized();
    
    try {
      final key = _getKey(eventId, collectionName);
      final jsonString = _prefs!.getString(key);
      
      if (jsonString == null) return null;
      
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final metadata = SyncMetadata.fromJson(json);
      
      return metadata.lastSyncTime;
    } catch (e) {
      LoggerUtils.logError('마지막 동기화 시간 조회 실패: $eventId/$collectionName', tag: _tag, error: e);
      return null;
    }
  }
  
  /// 특정 컬렉션의 동기화 메타데이터 조회
  Future<SyncMetadata?> getSyncMetadata(int eventId, String collectionName) async {
    await _ensureInitialized();
    
    try {
      final key = _getKey(eventId, collectionName);
      final jsonString = _prefs!.getString(key);
      
      if (jsonString == null) return null;
      
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return SyncMetadata.fromJson(json);
    } catch (e) {
      LoggerUtils.logError('동기화 메타데이터 조회 실패: $eventId/$collectionName', tag: _tag, error: e);
      return null;
    }
  }
  
  /// 동기화 메타데이터 저장
  Future<void> saveSyncMetadata(SyncMetadata metadata) async {
    await _ensureInitialized();
    
    try {
      final key = _getKey(metadata.eventId, metadata.collectionName);
      final jsonString = jsonEncode(metadata.toJson());
      
      await _prefs!.setString(key, jsonString);
      
      LoggerUtils.logDebug(
        '동기화 메타데이터 저장: ${metadata.eventId}/${metadata.collectionName} - ${metadata.lastSyncTime}',
        tag: _tag
      );
    } catch (e) {
      LoggerUtils.logError(
        '동기화 메타데이터 저장 실패: ${metadata.eventId}/${metadata.collectionName}',
        tag: _tag,
        error: e
      );
      rethrow;
    }
  }
  
  /// 마지막 동기화 시간 업데이트
  Future<void> updateLastSyncTime(int eventId, String collectionName, DateTime syncTime, {
    int? syncCount,
    String? syncHash,
  }) async {
    await _ensureInitialized();
    
    try {
      // 기존 메타데이터 조회
      final existing = await getSyncMetadata(eventId, collectionName);
      
      // 새로운 메타데이터 생성
      final metadata = SyncMetadata(
        collectionName: collectionName,
        eventId: eventId,
        lastSyncTime: syncTime,
        lastSyncCount: syncCount ?? existing?.lastSyncCount ?? 0,
        lastSyncHash: syncHash ?? existing?.lastSyncHash ?? '',
      );
      
      await saveSyncMetadata(metadata);
    } catch (e) {
      LoggerUtils.logError(
        '마지막 동기화 시간 업데이트 실패: $eventId/$collectionName',
        tag: _tag,
        error: e
      );
      rethrow;
    }
  }
  
  /// 특정 행사의 모든 동기화 메타데이터 삭제
  Future<void> clearEventSyncMetadata(int eventId) async {
    await _ensureInitialized();
    
    try {
      final keys = _prefs!.getKeys().where((key) => key.startsWith('${_keyPrefix}${eventId}_')).toList();
      
      for (final key in keys) {
        await _prefs!.remove(key);
      }
      
      LoggerUtils.logInfo('행사 $eventId 동기화 메타데이터 삭제 완료 (${keys.length}개)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 동기화 메타데이터 삭제 실패: $eventId', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 모든 동기화 메타데이터 삭제
  Future<void> clearAllSyncMetadata() async {
    await _ensureInitialized();
    
    try {
      final keys = _prefs!.getKeys().where((key) => key.startsWith(_keyPrefix)).toList();
      
      for (final key in keys) {
        await _prefs!.remove(key);
      }
      
      LoggerUtils.logInfo('모든 동기화 메타데이터 삭제 완료 (${keys.length}개)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('모든 동기화 메타데이터 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 특정 행사의 모든 컬렉션 동기화 메타데이터 조회
  Future<List<SyncMetadata>> getEventSyncMetadata(int eventId) async {
    await _ensureInitialized();
    
    try {
      final keys = _prefs!.getKeys().where((key) => key.startsWith('${_keyPrefix}${eventId}_')).toList();
      final metadataList = <SyncMetadata>[];
      
      for (final key in keys) {
        final jsonString = _prefs!.getString(key);
        if (jsonString != null) {
          try {
            final json = jsonDecode(jsonString) as Map<String, dynamic>;
            metadataList.add(SyncMetadata.fromJson(json));
          } catch (e) {
            LoggerUtils.logWarning('잘못된 동기화 메타데이터 형식: $key', tag: _tag, error: e);
          }
        }
      }
      
      return metadataList;
    } catch (e) {
      LoggerUtils.logError('행사 동기화 메타데이터 조회 실패: $eventId', tag: _tag, error: e);
      return [];
    }
  }
  
  /// 동기화가 필요한지 확인
  Future<bool> needsSync(int eventId, String collectionName, int remoteCount) async {
    final metadata = await getSyncMetadata(eventId, collectionName);
    
    if (metadata == null) return true; // 처음 동기화
    
    // 개수가 다르면 동기화 필요
    if (metadata.lastSyncCount != remoteCount) return true;
    
    // 마지막 동기화가 1시간 이상 지났으면 동기화 필요
    final hoursSinceLastSync = DateTime.now().difference(metadata.lastSyncTime).inHours;
    if (hoursSinceLastSync >= 1) return true;
    
    return false;
  }
  
  /// 키 생성
  String _getKey(int eventId, String collectionName) {
    return '${_keyPrefix}${eventId}_$collectionName';
  }
  
  /// 초기화 확인
  Future<void> _ensureInitialized() async {
    if (_prefs == null) {
      await initialize();
    }
  }
}
