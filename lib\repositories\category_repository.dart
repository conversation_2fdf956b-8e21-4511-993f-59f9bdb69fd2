import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/category.dart';
import '../services/database_service.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/logger_utils.dart';
import '../utils/app_colors.dart';


part 'category_repository.freezed.dart';

/// 카테고리 레포지토리의 상태를 나타내는 클래스
@freezed
abstract class CategoryRepositoryState with _$CategoryRepositoryState {
  const factory CategoryRepositoryState({
    @Default([]) List<Category> categories,
    @Default(false) bool isLoading,
    String? error,
  }) = _CategoryRepositoryState;
}

/// 카테고리 레포지토리 클래스
/// 카테고리 데이터의 CRUD 작업을 담당합니다.
class CategoryRepository extends StateNotifier<CategoryRepositoryState> {
  final DatabaseService _databaseService;
  final Ref _ref;
  StreamSubscription<List<Category>>? _categoryStreamSubscription;

  CategoryRepository(this._databaseService, this._ref) : super(const CategoryRepositoryState()) {
    // 초기화에서 자동 로딩하지 않음
  }

  /// 카테고리 목록 초기화 (필요시 수동 호출)
  void initializeCategories() {
    state = state.copyWith(isLoading: true);
    loadCategories();
  }

  /// 현재 이벤트의 모든 카테고리 로드
  Future<void> loadCategories({int? eventId}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final db = await _databaseService.database;
      
      // eventId가 없으면 현재 워크스페이스 ID 사용
      int? targetEventId = eventId;
      if (targetEventId == null) {
        final currentWorkspace = _ref.read(currentWorkspaceProvider);
        targetEventId = currentWorkspace?.id;
        if (targetEventId == null) {
          LoggerUtils.logError('현재 워크스페이스가 선택되지 않았습니다', tag: 'CategoryRepository');
          state = state.copyWith(isLoading: false, error: '현재 워크스페이스가 선택되지 않았습니다');
          return;
        }
      }
      
      final result = await db.query(
        DatabaseServiceImpl.categoriesTable,
        where: 'eventId = ?',
        whereArgs: [targetEventId],
        orderBy: 'sortOrder ASC, name ASC',
      );

      final categories = result.map((map) => Category.fromJson(map)).toList();
      
      state = state.copyWith(
        categories: categories,
        isLoading: false,
        error: null,
      );
      
      LoggerUtils.logInfo('Categories loaded: ${categories.length}', tag: 'CategoryRepository');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      LoggerUtils.logError('Failed to load categories', error: e, tag: 'CategoryRepository');
    }
  }

  /// 새 카테고리 추가
  Future<Category?> addCategory({
    required String name,
    int? eventId,
    int? sortOrder,
    int? color,
  }) async {
    try {
      final db = await _databaseService.database;
      
      // eventId가 없으면 현재 워크스페이스 ID 사용
      int? targetEventId = eventId;
      if (targetEventId == null) {
        final currentWorkspace = _ref.read(currentWorkspaceProvider);
        targetEventId = currentWorkspace?.id;
        if (targetEventId == null) {
          state = state.copyWith(error: '현재 워크스페이스가 선택되지 않았습니다.');
          return null;
        }
      }
      
      // 중복 이름 검사
      final existingCategories = await db.query(
        DatabaseServiceImpl.categoriesTable,
        where: 'name = ? AND eventId = ?',
        whereArgs: [name, targetEventId],
      );
      
      if (existingCategories.isNotEmpty) {
        state = state.copyWith(error: '이미 존재하는 카테고리 이름입니다.');
        return null;
      }

      // sortOrder가 없으면 마지막에 추가
      int finalSortOrder = sortOrder ?? 0;
      if (sortOrder == null) {
        final maxSortOrderResult = await db.rawQuery(
          'SELECT MAX(sortOrder) as maxSort FROM ${DatabaseServiceImpl.categoriesTable} WHERE eventId = ?',
          [targetEventId],
        );
        final maxSort = maxSortOrderResult.first['maxSort'] as int?;
        finalSortOrder = (maxSort ?? -1) + 1;
      }

      final category = Category(
        name: name,
        eventId: targetEventId,
        sortOrder: finalSortOrder,
        color: color ?? AppColors.categoryDefaultColorValue,
      );

      final id = await db.insert(
        DatabaseServiceImpl.categoriesTable,
        category.toMap(),
      );

      final savedCategory = category.copyWith(id: id);
      
      // 상태 업데이트
      await loadCategories(eventId: targetEventId);
      
      LoggerUtils.logInfo('Category added: $name', tag: 'CategoryRepository');
      return savedCategory;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      LoggerUtils.logError('Failed to add category', error: e, tag: 'CategoryRepository');
      return null;
    }
  }

  /// 카테고리 수정
  Future<bool> updateCategory({
    required int id,
    String? name,
    int? sortOrder,
    int? color,
  }) async {
    try {
      final db = await _databaseService.database;
      
      final updates = <String, dynamic>{};
      if (name != null) updates['name'] = name;
      if (sortOrder != null) updates['sortOrder'] = sortOrder;
      if (color != null) updates['color'] = color;
      
      if (updates.isEmpty) return true;

      final rowsAffected = await db.update(
        DatabaseServiceImpl.categoriesTable,
        updates,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rowsAffected > 0) {
        await loadCategories();
        LoggerUtils.logInfo('Category updated: $id', tag: 'CategoryRepository');
        return true;
      }
      
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      LoggerUtils.logError('Failed to update category', error: e, tag: 'CategoryRepository');
      return false;
    }
  }

  /// 카테고리 삭제
  Future<bool> deleteCategory(int id) async {
    try {
      final db = await _databaseService.database;
      
      // 카테고리에 속한 상품이 있는지 확인
      final productsInCategory = await db.query(
        DatabaseServiceImpl.productsTable,
        where: 'categoryId = ?',
        whereArgs: [id],
      );
      
      if (productsInCategory.isNotEmpty) {
        state = state.copyWith(error: '카테고리에 상품이 있어 삭제할 수 없습니다.');
        return false;
      }

      // 실제 삭제 (소프트 삭제가 아님)
      final rowsAffected = await db.delete(
        DatabaseServiceImpl.categoriesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rowsAffected > 0) {
        await loadCategories();
        LoggerUtils.logInfo('Category deleted: $id', tag: 'CategoryRepository');
        return true;
      }
      
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      LoggerUtils.logError('Failed to delete category', error: e, tag: 'CategoryRepository');
      return false;
    }
  }

  /// 카테고리 순서 재정렬
  Future<bool> reorderCategories(List<Category> reorderedCategories) async {
    try {
      final db = await _databaseService.database;
      
      await db.transaction((txn) async {
        for (int i = 0; i < reorderedCategories.length; i++) {
          await txn.update(
            DatabaseServiceImpl.categoriesTable,
            {'sortOrder': i},
            where: 'id = ?',
            whereArgs: [reorderedCategories[i].id],
          );
        }
      });

      await loadCategories();
      LoggerUtils.logInfo('Categories reordered', tag: 'CategoryRepository');
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      LoggerUtils.logError('Failed to reorder categories', error: e, tag: 'CategoryRepository');
      return false;
    }
  }

  /// ID로 카테고리 조회
  Future<Category?> getCategoryById(int id) async {
    try {
      final db = await _databaseService.database;
      final result = await db.query(
        DatabaseServiceImpl.categoriesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return Category.fromJson(result.first);
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('Failed to get category by id', error: e, tag: 'CategoryRepository');
      return null;
    }
  }

  /// 이벤트에 기본 카테고리 생성
  Future<void> createDefaultCategoryForEvent(int eventId) async {
    try {
      final db = await _databaseService.database;
      
      // 이미 기본 카테고리가 있는지 확인
      final existing = await db.query(
        DatabaseServiceImpl.categoriesTable,
        where: 'eventId = ?',
        whereArgs: [eventId],
      );

      if (existing.isEmpty) {
        final defaultCategory = Category(
          name: '기본 카테고리',
          eventId: eventId,
          sortOrder: 0,
          color: AppColors.categoryDefaultColorValue,
        );

        await db.insert(
          DatabaseServiceImpl.categoriesTable,
          defaultCategory.toMap(),
        );
        
        LoggerUtils.logInfo('Default category created for event: $eventId', tag: 'CategoryRepository');
      }
    } catch (e) {
      LoggerUtils.logError('Failed to create default category', error: e, tag: 'CategoryRepository');
    }
  }

  @override
  void dispose() {
    _categoryStreamSubscription?.cancel();
    super.dispose();
  }
}

/// 카테고리 레포지토리 Provider
final categoryRepositoryProvider = StateNotifierProvider<CategoryRepository, CategoryRepositoryState>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return CategoryRepository(databaseService, ref);
});

/// 현재 카테고리 목록을 제공하는 Provider
final categoriesProvider = Provider<List<Category>>((ref) {
  return ref.watch(categoryRepositoryProvider).categories;
});

/// 카테고리 로딩 상태를 제공하는 Provider
final categoryLoadingProvider = Provider<bool>((ref) {
  return ref.watch(categoryRepositoryProvider).isLoading;
});

/// 카테고리 에러 상태를 제공하는 Provider
final categoryErrorProvider = Provider<String?>((ref) {
  return ref.watch(categoryRepositoryProvider).error;
});
