import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';
import 'purchased_product.dart';

part 'prepayment.freezed.dart';
part 'prepayment.g.dart';

/// 선결제(예약) 정보를 표현하는 데이터 모델 클래스입니다.
/// - 구매자, 연락처, 금액, 상품목록, 날짜, 메모 등 다양한 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
@freezed
abstract class Prepayment with _$Prepayment {
  // 요일 관련 상수
  static const String noDayOfWeek = '없음';
  static const List<String> availableDaysOfWeek = [
    '월요일',
    '화요일',
    '수요일',
    '목요일',
    '금요일',
    '토요일',
    '일요일',
    noDayOfWeek,
  ];

  /// JSON 문자열을 요일 리스트로 변환합니다.
  static List<String> parsePickupDays(String pickupDaysJson) {
    try {
      if (pickupDaysJson.isEmpty) return [noDayOfWeek];
      
      // 쉼표로 구분된 요일들 처리 (예: "토요일, 일요일")
      if (pickupDaysJson.contains(',') && !pickupDaysJson.startsWith('[')) {
        final days = pickupDaysJson.split(',').map((day) => day.trim()).toList();
        return days.isNotEmpty ? days : [noDayOfWeek];
      }
      
      // JSON 배열 형태로 저장된 문자열을 파싱
      final List<dynamic> jsonList = jsonDecode(pickupDaysJson);
      return jsonList.map((e) => e.toString()).toList();
    } catch (e) {
      // 파싱 실패 시 기본값 반환
      return [noDayOfWeek];
    }
  }

  /// 요일 리스트를 JSON 문자열로 변환합니다.
  static String serializePickupDays(List<String> pickupDays) {
    return jsonEncode(pickupDays);
  }

  const Prepayment._();
  
  const factory Prepayment({
    required int id,
    required String buyerName,
    required String buyerContact,
    required int amount,
    required List<String> pickupDays,
    required String productNameList,
    String? purchasedProductsJson, // 구조화된 상품-수량 JSON
    String? memo,
    required DateTime registrationDate,
    @Default(false) bool isReceived,
    required int registrationActualDayOfWeek,
    required String bankName,
    required String email,
    String? twitterAccount,
    required int registrationTimestamp,
    @Default(1) int eventId, // 행사 ID 추가
    String? orderNumber, // 주문번호 추가
  }) = _Prepayment;

  factory Prepayment.fromJson(Map<String, dynamic> json) => _$PrepaymentFromJson(json);

  /// 상품-수량 구조를 반환 (purchasedProductsJson 우선, 없으면 productNameList fallback)
  List<PurchasedProduct> get purchasedProducts {
    // 1. purchasedProductsJson 우선
    if (purchasedProductsJson != null && purchasedProductsJson!.trim().isNotEmpty && purchasedProductsJson != '{}') {
      try {
        final List<dynamic> list = jsonDecode(purchasedProductsJson!);
        return list.map((e) => PurchasedProduct.fromJson(e as Map<String, dynamic>)).toList();
      } catch (e) {
        // fallback
      }
    }
    // 2. fallback: productNameList (수량=1)
    final productList = productNameList;
    if (productList.isEmpty) return [];
    try {
      final productNames = productList.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty);
      return productNames
          .map((name) => PurchasedProduct(
                name: name,
                quantity: 1,
                price: 0,
              ))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // SQLite 맵에서 직접 생성
  factory Prepayment.fromMap(Map<String, dynamic> map) {
    // registrationDate를 초 단위로 파싱
    final rawDateStr = map['registrationDate'] ?? DateTime.now().toIso8601String();
    final rawDate = DateTime.parse(rawDateStr);
    final registrationDate = DateTime(
      rawDate.year, rawDate.month, rawDate.day,
      rawDate.hour, rawDate.minute, rawDate.second
    );
    return Prepayment(
      id: map['id'],
      buyerName: map['buyerName'] ?? '',
      buyerContact: map['buyerContact'] ?? '',
      amount: map['amount'] ?? 0,
      pickupDays: parsePickupDays(map['pickupDayOfWeek'] ?? ''),
      productNameList: map['productNameList'] ?? '',
      purchasedProductsJson: map['purchasedProductsJson'],
      memo: map['memo'],
      registrationDate: registrationDate,
      isReceived: (map['isReceived'] ?? 0) == 1,
      registrationActualDayOfWeek: map['registrationActualDayOfWeek'] ?? 0,
      bankName: map['bankName'] ?? '',
      email: map['email'] ?? '',
      twitterAccount: map['twitterAccount'],
      registrationTimestamp: map['registrationTimestamp'] ?? 0,
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
      orderNumber: map['orderNumber'], // 주문번호 추가
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory Prepayment.create({
    required int id,
    required String buyerName,
    required String buyerContact,
    required int amount,
    required List<String> pickupDays,
    required List<PurchasedProduct> purchasedProducts,
    String? memo,
    required DateTime registrationDate,
    bool isReceived = false,
    required int registrationActualDayOfWeek,
    required String bankName,
    required String email,
    String? twitterAccount,
    int eventId = 1, // 행사 ID 추가
    String? orderNumber, // 주문번호 추가
  }) {
    final productNameList = purchasedProducts.map((e) => e.name).join(', ');
    final purchasedProductsJson = jsonEncode(purchasedProducts.map((e) => e.toJson()).toList());
    return Prepayment(
      id: id,
      buyerName: buyerName,
      buyerContact: buyerContact,
      amount: amount,
      pickupDays: pickupDays,
      productNameList: productNameList,
      purchasedProductsJson: purchasedProductsJson,
      memo: memo,
      registrationDate: registrationDate,
      isReceived: isReceived,
      registrationActualDayOfWeek: registrationActualDayOfWeek,
      bankName: bankName,
      email: email,
      twitterAccount: twitterAccount,
      registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
      eventId: eventId,
      orderNumber: orderNumber, // 주문번호 추가
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension PrepaymentMapper on Prepayment {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'buyerName': buyerName,
      'buyerContact': buyerContact,
      'amount': amount,
      'pickupDayOfWeek': Prepayment.serializePickupDays(pickupDays),
      'productNameList': productNameList,
      'purchasedProductsJson': purchasedProductsJson,
      'memo': memo,
      // registrationDate를 초 단위로 저장
      'registrationDate': DateTime(
        registrationDate.year, registrationDate.month, registrationDate.day,
        registrationDate.hour, registrationDate.minute, registrationDate.second
      ).toIso8601String(),
      'isReceived': isReceived ? 1 : 0,
      'registrationActualDayOfWeek': registrationActualDayOfWeek,
      'bankName': bankName,
      'email': email,
      'twitterAccount': twitterAccount,
      'registrationTimestamp': registrationTimestamp,
      'eventId': eventId,
      'orderNumber': orderNumber, // 주문번호 추가
    };
    if (id != 0) {
      map['id'] = id;
    }
    return map;
  }
}
