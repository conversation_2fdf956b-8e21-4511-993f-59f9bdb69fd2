import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'logger_utils.dart';
import '../providers/base_state.dart';

/// Provider들에서 공통으로 사용되는 에러 처리 유틸리티
class ProviderErrorHandler {
  /// 공통 에러 처리 및 상태 업데이트
  static T handleError<T extends BaseState>(
    T currentState,
    dynamic error,
    StackTrace stackTrace,
    String errorCode, {
    String? tag,
    String? operation,
  }) {
    final userFriendlyMessage = _getUserFriendlyErrorMessage(error);
    final technicalMessage = error.toString();

    // 로그 기록 (기술적 메시지)
    if (tag != null) {
      LoggerUtils.logError(
        operation != null ? '$operation 실패: $technicalMessage' : '작업 실패: $technicalMessage',
        tag: tag,
        error: error,
        stackTrace: stackTrace,
      );
    }

    // 상태 업데이트 (사용자 친화적 메시지)
    return currentState.copyWithBase(
      errorMessage: userFriendlyMessage,
      errorCode: errorCode,
      isLoading: false,
    ) as T;
  }

  /// 사용자 친화적 에러 메시지 생성
  static String _getUserFriendlyErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // 데이터베이스 관련 에러
    if (errorString.contains('database') || errorString.contains('sqlite')) {
      return '데이터 저장 중 문제가 발생했습니다. 잠시 후 다시 시도해주세요.';
    }

    // 네트워크 관련 에러
    if (errorString.contains('network') || errorString.contains('connection')) {
      return '네트워크 연결을 확인하고 다시 시도해주세요.';
    }

    // 메모리 관련 에러
    if (errorString.contains('memory') || errorString.contains('out of memory')) {
      return '메모리 부족으로 작업을 완료할 수 없습니다. 앱을 재시작해주세요.';
    }

    // 권한 관련 에러
    if (errorString.contains('permission') || errorString.contains('access denied')) {
      return '필요한 권한이 없습니다. 설정에서 권한을 확인해주세요.';
    }

    // 파일 관련 에러
    if (errorString.contains('file') || errorString.contains('directory')) {
      return '파일 처리 중 문제가 발생했습니다.';
    }

    // 기본 메시지
    return '작업 중 문제가 발생했습니다. 잠시 후 다시 시도해주세요.';
  }

  /// 비동기 작업 실행 및 에러 처리
  static Future<R> executeWithErrorHandling<R, T extends BaseState>({
    required Future<R> Function() operation,
    required StateNotifier<T> notifier,
    required String errorCode,
    String? tag,
    String? operationName,
    bool setLoadingState = true,
  }) async {
    // 로딩 상태 설정
    if (setLoadingState) {
      final currentState = notifier.mounted ? notifier.state : null;
      if (currentState != null) {
        final newState = currentState.copyWithBase(isLoading: true) as T;
        notifier.state = newState;
      }
    }

    try {
      if (tag != null && operationName != null) {
        LoggerUtils.logDebug('$operationName 시작', tag: tag);
      }
      
      final result = await operation();
      
      if (tag != null && operationName != null) {
        LoggerUtils.logDebug('$operationName 완료', tag: tag);
      }
      
      // 성공 시에만 로딩 상태 해제
      if (setLoadingState) {
        final currentState = notifier.mounted ? notifier.state : null;
        if (currentState != null) {
          final newState = currentState.copyWithBase(isLoading: false) as T;
          notifier.state = newState;
        }
      }
      
      return result;
    } catch (e, stackTrace) {
      final currentState = notifier.mounted ? notifier.state : null;
      if (currentState != null) {
        final errorState = handleError(
          currentState,
          e,
          stackTrace,
          errorCode,
          tag: tag,
          operation: operationName,
        );
        notifier.state = errorState;
        // 상태 반영을 보장하기 위해 마이크로태스크 큐를 한 번 돌림
        await Future.microtask(() {});
      }
      // 예외를 던지지 않고 상태만 업데이트
      throw StateError('Error occurred during operation');
    }
  }

  /// 간단한 CRUD 작업을 위한 헬퍼
  static Future<void> executeCrudOperation<T extends BaseState>({
    required Future<void> Function() operation,
    required StateNotifier<T> notifier,
    required String errorCode,
    required Future<void> Function() refreshData,
    String? tag,
    String? operationName,
    bool refreshAfterOperation = true,
  }) async {
    await executeWithErrorHandling<void, T>(
      operation: () async {
        await operation();
        if (refreshAfterOperation) {
          await refreshData();
        }
      },
      notifier: notifier,
      errorCode: errorCode,
      tag: tag,
      operationName: operationName,
      setLoadingState: false, // CRUD 작업에서는 로딩 상태를 자동으로 관리하지 않음
    );
  }
} 