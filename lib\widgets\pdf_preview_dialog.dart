import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import 'dart:io';
import 'dart:typed_data';
import '../services/pdf_export_service.dart';
import '../utils/app_colors.dart';
import '../utils/toast_utils.dart';
import '../utils/dialog_theme.dart' as custom_dialog;

/// PDF 내보내기 미리보기 다이얼로그
class PdfPreviewDialog extends ConsumerStatefulWidget {
  final Map<DateTime, Map<String, dynamic>> dailyStats;
  final List<Map<String, dynamic>> productStats;
  final Map<String, dynamic> fullStats; // 전체 통계 데이터
  final DateTimeRange? dateRange;
  final String eventName;
  final Map<String, GlobalKey> chartKeys; // 다중 차트 키
  final PdfExportType exportType; // 선택된 내보내기 타입

  const PdfPreviewDialog({
    super.key,
    required this.dailyStats,
    required this.productStats,
    required this.fullStats,
    this.dateRange,
    required this.eventName,
    required this.chartKeys,
    required this.exportType,
  });

  @override
  ConsumerState<PdfPreviewDialog> createState() => _PdfPreviewDialogState();
}

class _PdfPreviewDialogState extends ConsumerState<PdfPreviewDialog> {
  bool _isGenerating = false;
  
  @override
  Widget build(BuildContext context) {
    return custom_dialog.DialogTheme.buildResponsiveLargeDialog(
      child: Column(
          children: [
            // 헤더
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.primarySeed,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.picture_as_pdf,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'PDF 리포트 미리보기',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            
            // PDF 미리보기
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.neutral20),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: PdfPreview(
                    build: (format) => _generatePdf(),
                    allowSharing: false,
                    allowPrinting: false,
                    canChangePageFormat: false,
                    canDebug: false,
                    maxPageWidth: 700,
                    useActions: false,
                    loadingWidget: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'PDF를 생성하고 있습니다...',
                            style: TextStyle(
                              color: AppColors.neutral60,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            // 액션 버튼들
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.neutral50,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _isGenerating ? null : _downloadFile,
                      icon: Icon(
                        Icons.download,
                        size: 18,
                        color: AppColors.primarySeed,
                      ),
                      label: const Text('다운로드'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primarySeed,
                        side: BorderSide(color: AppColors.primarySeed),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isGenerating ? null : _shareFile,
                      icon: Icon(
                        Icons.share,
                        size: 18,
                        color: Colors.white,
                      ),
                      label: const Text('공유'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primarySeed,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        )
    );
  }

  /// PDF 생성
  Future<Uint8List> _generatePdf() async {
    final file = await PdfExportService.exportSalesData(
      dailyStats: widget.dailyStats,
      productStats: widget.productStats,
      fullStats: widget.fullStats,
      dateRange: widget.dateRange,
      eventName: widget.eventName,
      chartKeys: widget.chartKeys,
      exportType: widget.exportType,
    );
    
    final bytes = await file.readAsBytes();
    return Uint8List.fromList(bytes);
  }

  /// 파일 공유
  Future<void> _shareFile() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      final file = await PdfExportService.exportSalesData(
        dailyStats: widget.dailyStats,
        productStats: widget.productStats,
        fullStats: widget.fullStats,
        dateRange: widget.dateRange,
        eventName: widget.eventName,
        chartKeys: widget.chartKeys,
        exportType: widget.exportType,
      );

      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path)],
          text: '파라바라 매출 통계 리포트',
        ),
      );

      if (mounted) {
        Navigator.of(context).pop();
        ToastUtils.showSuccess(context, 'PDF가 공유되었습니다!');
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, 'PDF 공유 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  /// 파일 다운로드
  Future<void> _downloadFile() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      final file = await PdfExportService.exportSalesData(
        dailyStats: widget.dailyStats,
        productStats: widget.productStats,
        fullStats: widget.fullStats,
        dateRange: widget.dateRange,
        eventName: widget.eventName,
        chartKeys: widget.chartKeys,
        exportType: widget.exportType,
      );

      // 엑셀과 동일한 파일명 패턴 사용
      final now = DateTime.now();
      final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
      
      final dateString = widget.dateRange != null 
        ? '${widget.dateRange!.start.year}-${widget.dateRange!.start.month.toString().padLeft(2, '0')}-${widget.dateRange!.start.day.toString().padLeft(2, '0')}'
        : '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      
      final fileName = '파라바라_${widget.eventName}_${dateString}_$timestamp.pdf';
      
      // Android Downloads 폴더에 저장
      final downloadsDir = Directory('/storage/emulated/0/Download');
      final downloadPath = '${downloadsDir.path}/$fileName';
      final downloadFile = File(downloadPath);
      
      // 파일 복사
      await file.copy(downloadFile.path);
      
      if (mounted) {
        Navigator.of(context).pop();
        ToastUtils.showSuccess(
          context, 
          '파일이 다운로드 폴더에 저장되었습니다!\n📁 $fileName'
        );
        
        // 파일 자동으로 열기 (약간의 지연 후)
        Future.delayed(const Duration(milliseconds: 500), () async {
          try {
            final result = await OpenFile.open(downloadFile.path);
            if (result.type != ResultType.done) {
              if (mounted) {
                ToastUtils.showInfo(context, 
                  '자동 열기에 실패했습니다.\n'
                  '다운로드 폴더에서 ${fileName}을 직접 열어주세요.'
                );
              }
            }
          } catch (e) {
            if (mounted) {
              ToastUtils.showInfo(context, 
                '자동 열기에 실패했습니다.\n'
                '다운로드 폴더에서 ${fileName}을 직접 열어주세요.'
              );
            }
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, 'PDF 저장 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }
}
