/// 재시도 정책 설정 클래스
///
/// 비동기 작업의 재시도 동작을 정의합니다.
/// - 최대 재시도 횟수
/// - 지수 백오프 지연 시간
/// - 재시도 가능한 예외 타입
/// - 지연 시간 계산
class RetryPolicy {
  /// 최대 재시도 횟수
  final int maxRetries;

  /// 초기 지연 시간
  final Duration initialDelay;

  /// 백오프 계수 (지연 시간 증가 비율)
  final double backoffFactor;

  /// 최대 지연 시간
  final Duration maxDelay;

  /// 재시도 가능한 예외 판단 함수
  final bool Function(Object error) shouldRetry;

  /// 재시도 정책 생성
  ///
  /// [maxRetries]: 최대 재시도 횟수
  /// [initialDelay]: 초기 지연 시간
  /// [backoffFactor]: 백오프 계수
  /// [maxDelay]: 최대 지연 시간
  /// [shouldRetry]: 재시도 가능한 예외 판단 함수
  const RetryPolicy({
    required this.maxRetries,
    required this.initialDelay,
    required this.backoffFactor,
    required this.maxDelay,
    required this.shouldRetry,
  });

  /// Exception 타입에 대한 재시도 가능 여부 확인
  ///
  /// [e]: 확인할 Exception
  /// 반환값: 재시도 가능 여부
  bool shouldRetryException(Exception e) => shouldRetry(e);

  /// 재시도 횟수에 따른 지연 시간 계산
  ///
  /// 지수 백오프를 적용하여 지연 시간을 계산합니다.
  /// [retryCount]: 재시도 횟수 (1부터 시작)
  /// 반환값: 계산된 지연 시간
  Duration calculateDelay(int retryCount) {
    final delay = Duration(
      milliseconds: (initialDelay.inMilliseconds * (backoffFactor * retryCount))
          .toInt(),
    );
    return delay > maxDelay ? maxDelay : delay;
  }
} 