import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/settings_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/provider_exception.dart';
import 'base_async_notifier.dart';
import 'base_state.dart';

/// 설정 상태 클래스
class SettingsState extends BaseState {
  final int eventDayOfWeek;
  final bool collectDayOfWeekFromExcel;
  final int excelDayOfWeekColumnIndex;
  final bool linkPrepaymentToInventory;
  final int inventoryColumns;
  final int saleColumns;
  final int inventoryColumnsPortrait;
  final int inventoryColumnsLandscape;
  final int saleColumnsPortrait;
  final int saleColumnsLandscape;
  final bool isUpdating; // 추가: 업데이트 상태

  const SettingsState({
    required this.eventDayOfWeek,
    required this.collectDayOfWeekFromExcel,
    required this.excelDayOfWeekColumnIndex,
    required this.linkPrepaymentToInventory,
    required this.inventoryColumns,
    required this.saleColumns,
    required this.inventoryColumnsPortrait,
    required this.inventoryColumnsLandscape,
    required this.saleColumnsPortrait,
    required this.saleColumnsLandscape,
    required this.isUpdating, // 추가
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  SettingsState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  SettingsState copyWith({
    int? eventDayOfWeek,
    bool? collectDayOfWeekFromExcel,
    int? excelDayOfWeekColumnIndex,
    bool? linkPrepaymentToInventory,
    int? inventoryColumns,
    int? saleColumns,
    int? inventoryColumnsPortrait,
    int? inventoryColumnsLandscape,
    int? saleColumnsPortrait,
    int? saleColumnsLandscape,
    bool? isUpdating, // 추가
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return SettingsState(
      eventDayOfWeek: eventDayOfWeek ?? this.eventDayOfWeek,
      collectDayOfWeekFromExcel: collectDayOfWeekFromExcel ?? this.collectDayOfWeekFromExcel,
      excelDayOfWeekColumnIndex: excelDayOfWeekColumnIndex ?? this.excelDayOfWeekColumnIndex,
      linkPrepaymentToInventory: linkPrepaymentToInventory ?? this.linkPrepaymentToInventory,
      inventoryColumns: inventoryColumns ?? this.inventoryColumns,
      saleColumns: saleColumns ?? this.saleColumns,
      inventoryColumnsPortrait: inventoryColumnsPortrait ?? this.inventoryColumnsPortrait,
      inventoryColumnsLandscape: inventoryColumnsLandscape ?? this.inventoryColumnsLandscape,
      saleColumnsPortrait: saleColumnsPortrait ?? this.saleColumnsPortrait,
      saleColumnsLandscape: saleColumnsLandscape ?? this.saleColumnsLandscape,
      isUpdating: isUpdating ?? this.isUpdating, // 추가
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    eventDayOfWeek,
    collectDayOfWeekFromExcel,
    excelDayOfWeekColumnIndex,
    linkPrepaymentToInventory,
    inventoryColumns,
    saleColumns,
    inventoryColumnsPortrait,
    inventoryColumnsLandscape,
    saleColumnsPortrait,
    saleColumnsLandscape,
    isUpdating, // 추가
  ];
}

/// Settings Repository Provider
final settingsRepositoryProvider = Provider<SettingsRepository>((ref) {
  return SettingsRepository();
});

/// 앱 설정 상태를 관리하는 Provider입니다.
final settingsNotifierProvider = StateNotifierProvider<SettingsNotifier, AsyncValue<SettingsState>>((ref) {
  return SettingsNotifier(ref);
});

/// 앱 설정 상태 관리를 위한 Notifier 클래스입니다.
/// - 설정 값, CRUD, 상태 변화, 예외 처리 등 모든 비즈니스 로직 담당
class SettingsNotifier extends BaseAsyncNotifier<SettingsState> {
  static const String _tag = 'SettingsNotifier';
  static const String _domain = 'SET';

  bool _isPaused = false;

  SettingsNotifier(super.ref) {
    initialize();
  }



  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      cancelAllOperations();
      LoggerUtils.logDebug('SettingsNotifier paused', tag: _tag);
    }
  }

  void resume([Duration? delay]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('SettingsNotifier resumed', tag: _tag);
      initialize();
    }
  }

  Future<void> initialize() async {
    LoggerUtils.methodStart('initialize', tag: _tag);

    state = const AsyncValue.loading();

    try {
      final repository = ref.read(settingsRepositoryProvider);
      LoggerUtils.logInfo('설정 초기화 시작', tag: _tag);

      // 타블렛 감지 로직 - 저장된 기기 타입 사용
      bool isTablet = false;
      try {
        // 저장된 기기 타입 정보 사용 (UI에서 설정한 값)
        isTablet = await repository.getBool('device_is_tablet') ?? false;
        LoggerUtils.logInfo('저장된 기기 타입 사용: ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);
      } catch (e) {
        // 기본값으로 스마트폰 설정
        isTablet = false;
        LoggerUtils.logInfo('기기 타입 정보 없음 - 스마트폰으로 기본 설정', tag: _tag);
      }

      final eventDay = await runCancellableOperation(
        operationName: 'loadEventDay',
        operation: (_) async => await repository.getInt('event_day_of_week') ?? 7,
      );
      LoggerUtils.logInfo('이벤트 요일 초기화: $eventDay', tag: _tag);

      // 엑셀 관련 설정 로드
      final collectDayOfWeekFromExcel = await runCancellableOperation(
        operationName: 'loadCollectDayOfWeekFromExcel',
        operation: (_) async => await repository.getBool('collect_day_of_week_from_excel') ?? false,
      );
      LoggerUtils.logInfo('요일 수집 설정 초기화: $collectDayOfWeekFromExcel', tag: _tag);

      final excelDayOfWeekColumnIndex = await runCancellableOperation(
        operationName: 'loadExcelDayOfWeekColumnIndex',
        operation: (_) async => await repository.getInt('excel_day_of_week_column_index') ?? -1,
      );
      LoggerUtils.logInfo('요일 열 인덱스 초기화: $excelDayOfWeekColumnIndex', tag: _tag);

      final linkPrepaymentToInventory = await runCancellableOperation(
        operationName: 'loadLinkPrepaymentToInventory',
        operation: (_) async => await repository.getBool('link_prepayment_to_inventory') ?? false,
      );
      LoggerUtils.logInfo('재고 연동 설정 초기화: $linkPrepaymentToInventory', tag: _tag);

      // UI 열 수 설정 로드 (타블렛 감지에 따른 기본값 적용)
      final inventoryColumns = await runCancellableOperation(
        operationName: 'loadInventoryColumns',
        operation: (_) async => await repository.getInt('inventory_columns') ?? (isTablet ? 6 : 3),
      );
      LoggerUtils.logInfo('재고현황 열 수 초기화: $inventoryColumns', tag: _tag);

      final saleColumns = await runCancellableOperation(
        operationName: 'loadSaleColumns',
        operation: (_) async => await repository.getInt('sale_columns') ?? (isTablet ? 6 : 3),
      );
      LoggerUtils.logInfo('판매 화면 열 수 초기화: $saleColumns', tag: _tag);

      // 가로모드/세로모드별 열 수 설정 로드 (타블렛 감지에 따른 기본값 적용)
      final inventoryColumnsPortrait = await runCancellableOperation(
        operationName: 'loadInventoryColumnsPortrait',
        operation: (_) async => await repository.getInt('inventory_columns_portrait') ?? (isTablet ? 5 : 4),
      );
      LoggerUtils.logInfo('재고현황 세로모드 열 수 초기화: $inventoryColumnsPortrait', tag: _tag);

      final inventoryColumnsLandscape = await runCancellableOperation(
        operationName: 'loadInventoryColumnsLandscape',
        operation: (_) async => await repository.getInt('inventory_columns_landscape') ?? (isTablet ? 9 : 8),
      );
      LoggerUtils.logInfo('재고현황 가로모드 열 수 초기화: $inventoryColumnsLandscape', tag: _tag);

      final saleColumnsPortrait = await runCancellableOperation(
        operationName: 'loadSaleColumnsPortrait',
        operation: (_) async => await repository.getInt('sale_columns_portrait') ?? (isTablet ? 5 : 4),
      );
      LoggerUtils.logInfo('판매 화면 세로모드 열 수 초기화: $saleColumnsPortrait', tag: _tag);

      final saleColumnsLandscape = await runCancellableOperation(
        operationName: 'loadSaleColumnsLandscape',
        operation: (_) async => await repository.getInt('sale_columns_landscape') ?? (isTablet ? 9 : 8),
      );
      LoggerUtils.logInfo('판매 화면 가로모드 열 수 초기화: $saleColumnsLandscape', tag: _tag);

      final newState = SettingsState(
        eventDayOfWeek: eventDay,
        collectDayOfWeekFromExcel: collectDayOfWeekFromExcel,
        excelDayOfWeekColumnIndex: excelDayOfWeekColumnIndex,
        linkPrepaymentToInventory: linkPrepaymentToInventory,
        inventoryColumns: inventoryColumns,
        saleColumns: saleColumns,
        inventoryColumnsPortrait: inventoryColumnsPortrait,
        inventoryColumnsLandscape: inventoryColumnsLandscape,
        saleColumnsPortrait: saleColumnsPortrait,
        saleColumnsLandscape: saleColumnsLandscape,
        isUpdating: false, // 초기값 설정
      );
      
      LoggerUtils.logInfo('설정 초기화 완료 - 최종 상태: ${newState.toString()}', tag: _tag);
      if (mounted) { // mounted 체크 추가
        state = AsyncValue.data(newState);
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '설정 초기화 중 오류',
        error: e,
        stackTrace: stackTrace,
        tag: _tag,
      );
      if (mounted) { // mounted 체크 추가
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_INIT_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('initialize', tag: _tag);
  }

  Future<void> loadSettings({bool showLoading = true}) async {
    LoggerUtils.methodStart('loadSettings', tag: _tag);

    if (showLoading) {
      state = const AsyncValue.loading();
    }

    try {
      final repository = ref.read(settingsRepositoryProvider);
      
      // 타블렛 감지 로직 - 저장된 기기 타입 사용
      bool isTablet = false;
      try {
        // 저장된 기기 타입 정보 사용 (UI에서 설정한 값)
        isTablet = await repository.getBool('device_is_tablet') ?? false;
        LoggerUtils.logInfo('저장된 기기 타입 사용 (loadSettingsValues): ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);
      } catch (e) {
        // 기본값으로 스마트폰 설정
        isTablet = false;
        LoggerUtils.logInfo('기기 타입 정보 없음 (loadSettingsValues) - 스마트폰으로 기본 설정', tag: _tag);
      }
      
      final eventDay = await runCancellableOperation(
        operationName: 'loadEventDay',
        operation: (_) async => await repository.getInt('event_day_of_week') ?? 7,
      );
      LoggerUtils.logInfo('이벤트 요일 로드: $eventDay', tag: _tag);

      // 엑셀 관련 설정 로드
      final collectDayOfWeekFromExcel = await runCancellableOperation(
        operationName: 'loadCollectDayOfWeekFromExcel',
        operation: (_) async => await repository.getBool('collect_day_of_week_from_excel') ?? false,
      );
      LoggerUtils.logInfo('요일 수집 설정 로드: $collectDayOfWeekFromExcel', tag: _tag);

      final excelDayOfWeekColumnIndex = await runCancellableOperation(
        operationName: 'loadExcelDayOfWeekColumnIndex',
        operation: (_) async => await repository.getInt('excel_day_of_week_column_index') ?? -1,
      );
      LoggerUtils.logInfo('요일 열 인덱스 로드: $excelDayOfWeekColumnIndex', tag: _tag);

      final linkPrepaymentToInventory = await runCancellableOperation(
        operationName: 'loadLinkPrepaymentToInventory',
        operation: (_) async => await repository.getBool('link_prepayment_to_inventory') ?? false,
      );
      LoggerUtils.logInfo('재고 연동 설정 로드: $linkPrepaymentToInventory', tag: _tag);

      // UI 열 수 설정 로드 (타블렛 감지에 따른 기본값 적용)
      final inventoryColumns = await runCancellableOperation(
        operationName: 'loadInventoryColumns',
        operation: (_) async => await repository.getInt('inventory_columns') ?? (isTablet ? 6 : 3),
      );
      LoggerUtils.logInfo('재고현황 열 수 로드: $inventoryColumns', tag: _tag);

      final saleColumns = await runCancellableOperation(
        operationName: 'loadSaleColumns',
        operation: (_) async => await repository.getInt('sale_columns') ?? (isTablet ? 6 : 3),
      );
      LoggerUtils.logInfo('판매 화면 열 수 로드: $saleColumns', tag: _tag);

      // 가로모드/세로모드별 열 수 설정 로드 (타블렛 감지에 따른 기본값 적용)
      final inventoryColumnsPortrait = await runCancellableOperation(
        operationName: 'loadInventoryColumnsPortrait',
        operation: (_) async => await repository.getInt('inventory_columns_portrait') ?? (isTablet ? 5 : 4),
      );
      LoggerUtils.logInfo('재고현황 세로모드 열 수 로드: $inventoryColumnsPortrait', tag: _tag);

      final inventoryColumnsLandscape = await runCancellableOperation(
        operationName: 'loadInventoryColumnsLandscape',
        operation: (_) async => await repository.getInt('inventory_columns_landscape') ?? (isTablet ? 9 : 8),
      );
      LoggerUtils.logInfo('재고현황 가로모드 열 수 로드: $inventoryColumnsLandscape', tag: _tag);

      final saleColumnsPortrait = await runCancellableOperation(
        operationName: 'loadSaleColumnsPortrait',
        operation: (_) async => await repository.getInt('sale_columns_portrait') ?? (isTablet ? 5 : 4),
      );
      LoggerUtils.logInfo('판매 화면 세로모드 열 수 로드: $saleColumnsPortrait', tag: _tag);

      final saleColumnsLandscape = await runCancellableOperation(
        operationName: 'loadSaleColumnsLandscape',
        operation: (_) async => await repository.getInt('sale_columns_landscape') ?? (isTablet ? 9 : 8),
      );
      LoggerUtils.logInfo('판매 화면 가로모드 열 수 로드: $saleColumnsLandscape', tag: _tag);

      final newState = SettingsState(
        eventDayOfWeek: eventDay,
        collectDayOfWeekFromExcel: collectDayOfWeekFromExcel,
        excelDayOfWeekColumnIndex: excelDayOfWeekColumnIndex,
        linkPrepaymentToInventory: linkPrepaymentToInventory,
        inventoryColumns: inventoryColumns,
        saleColumns: saleColumns,
        inventoryColumnsPortrait: inventoryColumnsPortrait,
        inventoryColumnsLandscape: inventoryColumnsLandscape,
        saleColumnsPortrait: saleColumnsPortrait,
        saleColumnsLandscape: saleColumnsLandscape,
        isUpdating: false, // 초기값 설정
      );
      
      LoggerUtils.logInfo('설정 상태 업데이트 완료', tag: _tag);
      if (mounted) { // mounted 체크 추가
        state = AsyncValue.data(newState);
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '설정 로드 중 오류',
        error: e,
        stackTrace: stackTrace,
        tag: _tag,
      );
      if (mounted) { // mounted 체크 추가
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_LOAD_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('loadSettings', tag: _tag);
  }

  Future<void> setEventDayOfWeek(int dayOfWeek) async {
    LoggerUtils.methodStart('setEventDayOfWeek', tag: _tag);

    try {
      if (dayOfWeek < 1 || dayOfWeek > 7) {
        throw ProviderException(
          message: '요일은 1-7 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_DAY',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      await runCancellableOperation(
        operationName: 'setEventDay',
        operation: (_) async => await repository.setEventDayOfWeek(dayOfWeek),
      );

      // 값 저장 후 상태 즉시 갱신
      await loadSettings(showLoading: false);
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_EVENT_DAY_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setEventDayOfWeek', tag: _tag);
  }

  Future<void> deleteSetting(String key) async {
    LoggerUtils.methodStart('deleteSetting', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      await runCancellableOperation(
        operationName: 'deleteSetting',
        operation: (_) async => await repository.remove(key),
      );

      await loadSettings(showLoading: false);
    } catch (e, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_DELETE_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    } finally {
      if (state.value != null) {
        if (mounted) {
          state = AsyncValue.data(state.value!.copyWith(isUpdating: false));
        }
      }
      LoggerUtils.methodEnd('deleteSetting', tag: _tag);
    }
  }

  // 엑셀 관련 설정 메서드들
  Future<void> setCollectDayOfWeekFromExcel(bool collect) async {
    LoggerUtils.methodStart('setCollectDayOfWeekFromExcel', tag: _tag);
    LoggerUtils.logInfo('요일 수집 설정 변경: $collect', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setCollectDayOfWeekFromExcel',
        operation: (_) async => await repository.setBool('collect_day_of_week_from_excel', collect),
      );

      if (success) {
        LoggerUtils.logInfo('요일 수집 설정 저장 성공: $collect', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('요일 수집 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '요일 수집 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_COLLECT_DAY_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setCollectDayOfWeekFromExcel', tag: _tag);
  }

  Future<void> setExcelDayOfWeekColumnIndex(int columnIndex) async {
    LoggerUtils.methodStart('setExcelDayOfWeekColumnIndex', tag: _tag);
    LoggerUtils.logInfo('요일 열 인덱스 설정 변경: $columnIndex', tag: _tag);

    try {
      if (columnIndex < -1) {
        throw ProviderException(
          message: '열 인덱스는 -1 이상이어야 합니다',
          code: '${_domain}_INVALID_COLUMN_INDEX',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setExcelDayOfWeekColumnIndex',
        operation: (_) async => await repository.setInt('excel_day_of_week_column_index', columnIndex),
      );

      if (success) {
        LoggerUtils.logInfo('요일 열 인덱스 설정 저장 성공: $columnIndex', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('요일 열 인덱스 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '요일 열 인덱스 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_COLUMN_INDEX_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setExcelDayOfWeekColumnIndex', tag: _tag);
  }

  Future<void> setLinkPrepaymentToInventory(bool link) async {
    LoggerUtils.methodStart('setLinkPrepaymentToInventory', tag: _tag);
    LoggerUtils.logInfo('재고 연동 설정 변경: $link', tag: _tag);

    try {
      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setLinkPrepaymentToInventory',
        operation: (_) async => await repository.setBool('link_prepayment_to_inventory', link),
      );

      if (success) {
        LoggerUtils.logInfo('재고 연동 설정 저장 성공: $link', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고 연동 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고 연동 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_LINK_INVENTORY_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setLinkPrepaymentToInventory', tag: _tag);
  }

  Future<void> setInventoryColumns(int columns) async {
    LoggerUtils.methodStart('setInventoryColumns', tag: _tag);
    LoggerUtils.logInfo('재고현황 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setInventoryColumns',
        operation: (_) async => await repository.setInt('inventory_columns', columns),
      );

      if (success) {
        LoggerUtils.logInfo('재고현황 열 수 설정 저장 성공: $columns', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고현황 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고현황 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_INVENTORY_COLUMNS_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setInventoryColumns', tag: _tag);
  }

  Future<void> setSaleColumns(int columns) async {
    LoggerUtils.methodStart('setSaleColumns', tag: _tag);
    LoggerUtils.logInfo('판매 화면 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setSaleColumns',
        operation: (_) async => await repository.setInt('sale_columns', columns),
      );

      if (success) {
        LoggerUtils.logInfo('판매 화면 열 수 설정 저장 성공: $columns', tag: _tag);
        // 전체 설정을 다시 로드하여 일관성 보장
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('판매 화면 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '판매 화면 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SALE_COLUMNS_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setSaleColumns', tag: _tag);
  }

  Future<void> setInventoryColumnsPortrait(int columns) async {
    LoggerUtils.methodStart('setInventoryColumnsPortrait', tag: _tag);
    LoggerUtils.logInfo('재고현황 세로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setInventoryColumnsPortrait',
        operation: (_) async => await repository.setInt('inventory_columns_portrait', columns),
      );

      if (success) {
        LoggerUtils.logInfo('재고현황 세로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고현황 세로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고현황 세로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_INVENTORY_COLUMNS_PORTRAIT_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setInventoryColumnsPortrait', tag: _tag);
  }

  Future<void> setInventoryColumnsLandscape(int columns) async {
    LoggerUtils.methodStart('setInventoryColumnsLandscape', tag: _tag);
    LoggerUtils.logInfo('재고현황 가로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setInventoryColumnsLandscape',
        operation: (_) async => await repository.setInt('inventory_columns_landscape', columns),
      );

      if (success) {
        LoggerUtils.logInfo('재고현황 가로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('재고현황 가로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '재고현황 가로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_INVENTORY_COLUMNS_LANDSCAPE_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setInventoryColumnsLandscape', tag: _tag);
  }

  Future<void> setSaleColumnsPortrait(int columns) async {
    LoggerUtils.methodStart('setSaleColumnsPortrait', tag: _tag);
    LoggerUtils.logInfo('판매 화면 세로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setSaleColumnsPortrait',
        operation: (_) async => await repository.setInt('sale_columns_portrait', columns),
      );

      if (success) {
        LoggerUtils.logInfo('판매 화면 세로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('판매 화면 세로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '판매 화면 세로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SALE_COLUMNS_PORTRAIT_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setSaleColumnsPortrait', tag: _tag);
  }

  Future<void> setSaleColumnsLandscape(int columns) async {
    LoggerUtils.methodStart('setSaleColumnsLandscape', tag: _tag);
    LoggerUtils.logInfo('판매 화면 가로모드 열 수 설정 변경: $columns', tag: _tag);

    try {
      if (columns < 3 || columns > 12) {
        throw ProviderException(
          message: '열 수는 3-12 사이의 값이어야 합니다',
          code: '${_domain}_INVALID_COLUMNS',
        );
      }

      if (!mounted) return;
      state = AsyncValue.data(state.value!.copyWith(isUpdating: true));

      final repository = ref.read(settingsRepositoryProvider);
      final success = await runCancellableOperation(
        operationName: 'setSaleColumnsLandscape',
        operation: (_) async => await repository.setInt('sale_columns_landscape', columns),
      );

      if (success) {
        LoggerUtils.logInfo('판매 화면 가로모드 열 수 설정 저장 성공: $columns', tag: _tag);
        await loadSettings(showLoading: false);
      } else {
        LoggerUtils.logError('판매 화면 가로모드 열 수 설정 저장 실패', tag: _tag);
        throw Exception('설정 저장에 실패했습니다');
      }
    } catch (e, stackTrace) {
      if (mounted) {
        LoggerUtils.logError(
          '판매 화면 가로모드 열 수 설정 변경 중 오류',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );
        state = AsyncValue.error(
          ProviderException.wrap(
            e is Exception ? e : Exception(e.toString()),
            code: '${_domain}_SET_SALE_COLUMNS_LANDSCAPE_ERROR',
            stackTrace: stackTrace,
          ),
          stackTrace,
        );
      }
    }

    LoggerUtils.methodEnd('setSaleColumnsLandscape', tag: _tag);
  }

  /// 기기 타입 설정 (UI에서 화면 크기 기반으로 감지한 결과를 전달받음)
  Future<void> setDeviceType(bool isTablet) async {
    LoggerUtils.methodStart('setDeviceType', tag: _tag);
    LoggerUtils.logInfo('기기 타입 설정: ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);

    try {
      if (!mounted) return;

      final repository = ref.read(settingsRepositoryProvider);

      // 기존 기기 타입 확인
      final previousDeviceType = await repository.getBool('device_is_tablet');
      LoggerUtils.logInfo('이전 기기 타입: ${previousDeviceType == null ? "미설정" : (previousDeviceType ? "태블릿" : "스마트폰")}', tag: _tag);

      // 기기 타입을 저장
      await repository.setBool('device_is_tablet', isTablet);

      // 현재 설정값이 없는 경우에만 기본값 적용
      await _setDefaultColumnsIfNotSet(repository, isTablet);

      // 설정 다시 로드
      await loadSettings(showLoading: false);

      LoggerUtils.logInfo('기기 타입 설정 완료', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('기기 타입 설정 실패', tag: _tag, error: e, stackTrace: stackTrace);
    } finally {
      LoggerUtils.methodEnd('setDeviceType', tag: _tag);
    }
  }

  /// 설정되지 않은 열 수 설정에 대해서만 기본값 적용
  Future<void> _setDefaultColumnsIfNotSet(SettingsRepository repository, bool isTablet) async {
    // 재고현황 세로모드
    final inventoryPortrait = await repository.getInt('inventory_columns_portrait');
    if (inventoryPortrait == null) {
      await repository.setInt('inventory_columns_portrait', isTablet ? 5 : 4);
    }

    // 재고현황 가로모드
    final inventoryLandscape = await repository.getInt('inventory_columns_landscape');
    if (inventoryLandscape == null) {
      await repository.setInt('inventory_columns_landscape', isTablet ? 9 : 8);
    }

    // 판매 화면 세로모드
    final salePortrait = await repository.getInt('sale_columns_portrait');
    if (salePortrait == null) {
      await repository.setInt('sale_columns_portrait', isTablet ? 5 : 4);
    }

    // 판매 화면 가로모드
    final saleLandscape = await repository.getInt('sale_columns_landscape');
    if (saleLandscape == null) {
      await repository.setInt('sale_columns_landscape', isTablet ? 9 : 8);
    }
  }


}

/// Helper providers
final eventDayOfWeekProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.eventDayOfWeek,
        error: (_, __) => 7, // 기본값: 일요일
        loading: () => 7,
      );
});

final settingsIsUpdatingProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.isUpdating,
        error: (_, __) => false,
        loading: () => true,
      );
});

// 엑셀 관련 설정 Provider들
final collectDayOfWeekFromExcelProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.collectDayOfWeekFromExcel,
        error: (_, __) => false,
        loading: () => false,
      );
});

final excelDayOfWeekColumnIndexProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.excelDayOfWeekColumnIndex,
        error: (_, __) => -1,
        loading: () => -1,
      );
});

final linkPrepaymentToInventoryProvider = Provider<bool>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.linkPrepaymentToInventory,
        error: (_, __) => false,
        loading: () => false,
      );
});

// UI 열 수 설정 Provider들
final inventoryColumnsProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumns,
        error: (_, __) => 6, // 타블렛 기본값으로 변경 (세로 3, 가로 5 → 세로 4, 가로 6)
        loading: () => 6,
      );
});

final saleColumnsProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.saleColumns,
        error: (_, __) => 6, // 타블렛 기본값으로 변경 (세로 3, 가로 5 → 세로 4, 가로 6)
        loading: () => 6,
      );
});

// 가로모드/세로모드별 열 수 설정 Provider들
final inventoryColumnsPortraitProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsPortrait,
        error: (_, __) => 4, // 타블렛 세로모드 기본값으로 변경
        loading: () => 4,
      );
});

final inventoryColumnsLandscapeProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsLandscape,
        error: (_, __) => 8, // 타블렛 가로모드 기본값으로 변경
        loading: () => 8,
      );
});

// 통합된 UI 열 수 설정: 판매 화면도 재고현황과 같은 설정 사용
final saleColumnsPortraitProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsPortrait, // 재고현황과 통합
        error: (_, __) => 4, // 타블렛 세로모드 기본값으로 변경
        loading: () => 4,
      );
});

final saleColumnsLandscapeProvider = Provider<int>((ref) {
  return ref
      .watch(settingsNotifierProvider)
      .when(
        data: (state) => state.inventoryColumnsLandscape, // 재고현황과 통합
        error: (_, __) => 8, // 타블렛 가로모드 기본값으로 변경
        loading: () => 8,
      );
});

// 호환성을 위한 별칭
final eventDaysProvider = eventDayOfWeekProvider;



