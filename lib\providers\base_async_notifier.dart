import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import 'package:riverpod/riverpod.dart';

import '../utils/logger_utils.dart';
import 'base_state.dart';
import 'retry_policy.dart' as retry_policy;
import '../utils/provider_exception.dart';
import '../utils/cancellation_token.dart';

/// 비동기 상태를 관리하는 기본 Notifier 클래스입니다.
/// - 로딩/에러/취소 상태 등 공통 상태 관리
/// - 비동기 작업 취소 및 재시도 기능 제공
abstract class BaseAsyncNotifier<T extends BaseState> extends StateNotifier<AsyncValue<T>> {
  final Ref ref;

  BaseAsyncNotifier(this.ref) : super(const AsyncValue.loading());

  /// 재시도 가능한 예외 목록
  static final List<Type> retryableExceptions = [
    TimeoutException,
    SocketException,
    DatabaseException,
  ];

  /// 기본 재시도 정책
  static final retry_policy.RetryPolicy defaultRetryPolicy = retry_policy.RetryPolicy(
    maxRetries: 3,
    initialDelay: const Duration(milliseconds: 500),
    backoffFactor: 2.0,
    maxDelay: const Duration(seconds: 5),
    shouldRetry: (e) =>
        retryableExceptions.any((type) => e.runtimeType == type),
  );

  /// 현재 실행 중인 비동기 작업들의 취소 토큰
  final Map<String, CancellationToken> _cancellationTokens = {};
  bool _isPaused = false;

  /// Provider가 일시 중지되었는지 여부
  bool get isPaused => _isPaused;

  /// Provider 일시 중지
  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      cancelAllOperations();
      LoggerUtils.logDebug('Provider paused', tag: runtimeType.toString());
    }
  }

  /// Provider 재개
  /// [delay] - 지연 시간 후 재개 (선택사항)
  void resume([Duration? delay]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('Provider resumed', tag: runtimeType.toString());
    }
  }

  /// 작업 취소 토큰 생성
  CancellationToken _createCancellationToken(String operationName) {
    final token = CancellationToken();
    _cancellationTokens[operationName] = token;
    return token;
  }

  /// 작업 취소 토큰 제거
  void _removeCancellationToken(String operationName) {
    _cancellationTokens.remove(operationName);
  }

  /// 모든 진행 중인 작업 취소
  void cancelAllOperations() {
    final tokenCount = _cancellationTokens.length;

    for (final entry in _cancellationTokens.entries) {
      try {
        entry.value.cancel();
      } catch (e) {
        LoggerUtils.logWarning(
          'Failed to cancel operation "${entry.key}": $e',
          tag: runtimeType.toString(),
        );
      }
    }
    _cancellationTokens.clear();

    if (tokenCount > 0) {
      LoggerUtils.logDebug(
        'Cancelled $tokenCount operations',
        tag: runtimeType.toString(),
      );
    }
  }

  /// 특정 작업 취소
  void cancelOperation(String operationName) {
    final token = _cancellationTokens[operationName];
    if (token != null) {
      token.cancel();
      _removeCancellationToken(operationName);
    }
  }

  /// 취소 가능한 비동기 작업 실행
  Future<R> runCancellableOperation<R>({
    required String operationName,
    required Future<R> Function(CancellationToken) operation,
    retry_policy.RetryPolicy? retryPolicy,
  }) async {
    if (_isPaused) {
      throw Exception('Provider가 일시 중지되었습니다: $operationName');
    }

    final token = _createCancellationToken(operationName);
    
    try {
      return await operation(token);
    } catch (e) {
      if (e is CancelledException) {
        throw ProviderException.general(
          '작업이 취소되었습니다',
          code: 'OPERATION_CANCELLED',
        );
      }
      rethrow;
    } finally {
      _removeCancellationToken(operationName);
    }
  }

  /// 비동기 작업 처리 (하위 호환성을 위한 래퍼)
  Future<R> handleAsyncOperation<R>({
    required String operationName,
    required Future<R> Function() operation,
    retry_policy.RetryPolicy? retryPolicy,
  }) async {
    return runCancellableOperation(
      operationName: operationName,
      operation: (_) => operation(),
    );
  }

  /// 상태 업데이트
  void updateState(T Function(T currentState) update) {
    if (state.value == null) return;
    state = AsyncValue.data(update(state.value!));
  }

  /// 에러 상태 업데이트
  void updateError(ProviderException error, StackTrace? stackTrace) {
    state = AsyncValue.error(error, stackTrace ?? StackTrace.current);
  }

  /// 로딩 상태 설정
  void setLoading(bool isLoading) {
    if (state.value == null) return;
    state = AsyncValue.data(state.value!.copyWithBase(isLoading: isLoading) as T);
  }

  /// 에러 상태 설정
  void setError(
    String message, {
    String? code,
    Map<String, dynamic>? context,
  }) {
    if (state.value == null) return;
    final error = ProviderException(
      message: message,
      code: code ?? 'GENERAL_ERROR',
      context: context,
    );
    state = AsyncValue.error(error, StackTrace.current);
  }

  /// 에러 상태 초기화
  void clearError() {
    if (state.value == null) return;
    state = AsyncValue.data(state.value!.copyWithBase(
      errorMessage: null,
      errorCode: null,
      errorDetails: null,
    ) as T);
  }

  /// 상태 비교 메서드 (오버라이드 가능)
  bool shouldUpdateState(T oldState, T newState) {
    return oldState != newState;
  }

  /// Provider 정리 작업 등록
  @override
  void dispose() {
    cancelAllOperations();
    super.dispose();
  }
}

// CancellationToken은 lib/utils/cancellation_token.dart에서 import됨 