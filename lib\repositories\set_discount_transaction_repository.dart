import '../models/set_discount_transaction.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// SetDiscountTransaction 데이터베이스 작업을 담당하는 Repository 클래스
class SetDiscountTransactionRepository {
  final DatabaseService _databaseService;
  static const String tableName = 'set_discount_transactions';

  SetDiscountTransactionRepository({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseServiceImpl();

  /// 테이블 생성 SQL
  static String get createTableSql => '''
    CREATE TABLE $tableName (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      batchSaleId TEXT NOT NULL,
      appliedDiscounts TEXT NOT NULL,
      totalDiscountAmount INTEGER NOT NULL DEFAULT 0,
      appliedCount INTEGER NOT NULL DEFAULT 0,
      createdAt TEXT NOT NULL,
      eventId INTEGER NOT NULL DEFAULT 1,
      FOREIGN KEY (eventId) REFERENCES events (id) ON DELETE CASCADE
    )
  ''';

  /// 인덱스 생성 SQL
  static List<String> get createIndexSqls => [
    'CREATE INDEX idx_set_discount_transactions_batch_sale_id ON $tableName (batchSaleId)',
    'CREATE INDEX idx_set_discount_transactions_event_id ON $tableName (eventId)',
    'CREATE INDEX idx_set_discount_transactions_created_at ON $tableName (createdAt)',
  ];

  /// 세트 할인 거래 정보 저장
  Future<int> insert(SetDiscountTransaction transaction) async {
    try {
      final db = await _databaseService.database;
      final id = await db.insert(tableName, transaction.toMap());
      LoggerUtils.logInfo('SetDiscountTransaction inserted with id: $id');
      return id;
    } catch (e) {
      LoggerUtils.logError('Failed to insert SetDiscountTransaction: $e');
      rethrow;
    }
  }

  /// 배치 판매 ID로 세트 할인 거래 정보 조회
  Future<SetDiscountTransaction?> getByBatchSaleId(String batchSaleId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'batchSaleId = ?',
        whereArgs: [batchSaleId],
      );

      if (maps.isNotEmpty) {
        return SetDiscountTransaction.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('Failed to get SetDiscountTransaction by batchSaleId: $e');
      return null;
    }
  }

  /// 이벤트별 모든 세트 할인 거래 정보 조회
  Future<List<SetDiscountTransaction>> getAllByEventId(int eventId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'eventId = ?',
        whereArgs: [eventId],
        orderBy: 'createdAt DESC',
      );

      return maps.map((map) => SetDiscountTransaction.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get SetDiscountTransactions by eventId: $e');
      return [];
    }
  }

  /// 날짜 범위별 세트 할인 거래 정보 조회
  Future<List<SetDiscountTransaction>> getByDateRange({
    required int eventId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'eventId = ? AND createdAt >= ? AND createdAt <= ?',
        whereArgs: [
          eventId,
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
        orderBy: 'createdAt DESC',
      );

      return maps.map((map) => SetDiscountTransaction.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get SetDiscountTransactions by date range: $e');
      return [];
    }
  }

  /// 세트 할인 거래 정보 업데이트
  Future<int> update(SetDiscountTransaction transaction) async {
    try {
      final db = await _databaseService.database;
      final count = await db.update(
        tableName,
        transaction.toMap(),
        where: 'id = ?',
        whereArgs: [transaction.id],
      );
      LoggerUtils.logInfo('SetDiscountTransaction updated: $count rows affected');
      return count;
    } catch (e) {
      LoggerUtils.logError('Failed to update SetDiscountTransaction: $e');
      rethrow;
    }
  }

  /// 배치 판매 ID로 세트 할인 거래 정보 삭제
  Future<int> deleteByBatchSaleId(String batchSaleId) async {
    try {
      final db = await _databaseService.database;
      final count = await db.delete(
        tableName,
        where: 'batchSaleId = ?',
        whereArgs: [batchSaleId],
      );
      LoggerUtils.logInfo('SetDiscountTransaction deleted by batchSaleId: $count rows affected');
      return count;
    } catch (e) {
      LoggerUtils.logError('Failed to delete SetDiscountTransaction by batchSaleId: $e');
      rethrow;
    }
  }

  /// ID로 세트 할인 거래 정보 삭제
  Future<int> delete(int id) async {
    try {
      final db = await _databaseService.database;
      final count = await db.delete(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
      );
      LoggerUtils.logInfo('SetDiscountTransaction deleted: $count rows affected');
      return count;
    } catch (e) {
      LoggerUtils.logError('Failed to delete SetDiscountTransaction: $e');
      rethrow;
    }
  }

  /// 이벤트별 모든 세트 할인 거래 정보 삭제
  Future<int> deleteAllByEventId(int eventId) async {
    try {
      final db = await _databaseService.database;
      final count = await db.delete(
        tableName,
        where: 'eventId = ?',
        whereArgs: [eventId],
      );
      LoggerUtils.logInfo('All SetDiscountTransactions deleted for eventId $eventId: $count rows affected');
      return count;
    } catch (e) {
      LoggerUtils.logError('Failed to delete all SetDiscountTransactions by eventId: $e');
      rethrow;
    }
  }

  /// 세트 할인 통계 조회
  Future<Map<String, dynamic>> getStatistics(int eventId) async {
    try {
      final db = await _databaseService.database;

      // 총 세트 할인 적용 횟수와 총 할인 금액
      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT
          SUM(appliedCount) as totalAppliedCount,
          SUM(totalDiscountAmount) as totalDiscountAmount,
          COUNT(*) as totalTransactions
        FROM $tableName
        WHERE eventId = ?
      ''', [eventId]);

      final stats = result.first;
      return {
        'totalAppliedCount': stats['totalAppliedCount'] ?? 0,
        'totalDiscountAmount': stats['totalDiscountAmount'] ?? 0,
        'totalTransactions': stats['totalTransactions'] ?? 0,
      };
    } catch (e) {
      LoggerUtils.logError('Failed to get SetDiscountTransaction statistics: $e');
      return {
        'totalAppliedCount': 0,
        'totalDiscountAmount': 0,
        'totalTransactions': 0,
      };
    }
  }
}
